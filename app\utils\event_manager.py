import asyncio
import json
from datetime import datetime
from typing import Dict, AsyncGenerator, Any, Optional
from pydantic import BaseModel
from app.logger import logger

class AgentStatusUpdate(BaseModel):
    """
    Agent 流程状态更新的数据模型。
    """
    event_type: str  # 事件类型，例如 "agent_selection", "tool_call_start", "thought", "final_result"
    timestamp: str   # 时间戳，ISO 格式
    user_id: str     # 用户 ID
    session_id: Optional[str] = None # 会话 ID
    data: Dict[str, Any] # 具体的事件数据，根据 event_type 包含不同内容

class EventManager:
    def __init__(self):
        self.connections: Dict[str, asyncio.Queue] = {} # {user_id: Queue}

    async def connect(self, user_id: str) -> AsyncGenerator[str, None]:
        """
        为指定用户建立 SSE 连接，并返回一个异步生成器。
        """
        queue = asyncio.Queue()
        self.connections[user_id] = queue
        try:
            while True:
                message = await queue.get()
                yield f"data: {json.dumps(message)}\n\n"
        except asyncio.CancelledError:
            # 连接关闭时清理
            del self.connections[user_id]
            logger.info(f"SSE connection for {user_id} closed.")

    async def publish(self, user_id: str, event_type: str, data: Dict[str, Any], session_id: Optional[str] = None):
        """
        向指定用户发布状态更新事件。
        """
        if user_id in self.connections:
            update = AgentStatusUpdate(
                event_type=event_type,
                timestamp=datetime.now().isoformat(),
                user_id=user_id,
                session_id=session_id,
                data=data
            )
            await self.connections[user_id].put(update.dict())
            logger.info(f"Published event '{event_type}' for user {user_id}")