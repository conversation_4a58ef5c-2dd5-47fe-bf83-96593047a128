import json
from typing import AsyncGenerator, List, Optional, Union, Dict, Any

from pydantic import model_validator
from app.llm.llm import LLM
from app.logger import logger
from app.agent.base_agent import BaseAgent
from app.prompt.qa import USER_PROMPT, SYSTEM_PROMPT
from app.schema import AgentState, Message
from app.api.api_schema import ChatAPIOutput # 导入 ChatAPIOutput
from app.utils.event_manager import EventManager # 导入 EventManager


class QaAgent(BaseAgent):
    """
    问答助手，快速解答用户问题，具备图像分析能力。
    """

    name: str = "qa_agent"
    description: str = """你是一个问答助手，快速解答用户问题，具备图像分析能力。功能包括：
    1.回答通用知识问题，如“今天几号”、“1+1=？”。
    2.分析图像内容，提取信息，如“图中是什么动物？”（识别为“猫”）。"""

    system_prompt: Optional[str] = SYSTEM_PROMPT # 修改为 Optional[str]
    user_prompt: str = USER_PROMPT

    @model_validator(mode="after")
    def initialize_agent(self) -> "QaAgent":
        if self.llm is None or not isinstance(self.llm, LLM):
            self.llm = LLM(config_name="vision")
        return self

    async def run(self, user_id: str, request: Optional[str] = None, image_urls: Optional[List[str]] = None, with_history: bool = True,event_manager: Optional[EventManager] = None, session_id: Optional[str] = None,sn: Optional[str] = None  ) -> Union[str, AsyncGenerator[str, None]]:
        # 1.获取用户会话
        self.event_manager = event_manager # 接收并保存 event_manager
        memory = self.session_storage.get_session(user_id=user_id)
        self.memory = memory  # 将当前用户的 memory 绑定到代理

        # 2.保存用户请求到记忆。
        if request:
            if image_urls:
                self.memory.update_memory("user", self.user_prompt.format(request=request), image_urls=image_urls)
            else:
                self.memory.update_memory("user", self.user_prompt.format(request=request)) 
                
        try:
            # 3.调用 LLM：
            system_message = Message.system_message(content=self.system_prompt)
            
            full_response_content = ""
            # 显式转换 messages 类型，确保兼容性
            messages_for_llm: List[Union[dict, Message]] = [
                Message(**d) if isinstance(d, dict) and "role" in d and "content" in d else d
                for d in self.memory.to_dict_list()
            ]
            
            # 确保 llm.ask 返回的是 AsyncGenerator
            llm_ask_result = await self.llm.ask(
                messages = messages_for_llm, 
                system_msgs = [system_message],
                stream=True # 确保是流式调用
            )
            # 断言 llm_ask_result 是 AsyncGenerator
            assert isinstance(llm_ask_result, AsyncGenerator), "llm.ask with stream=True did not return an AsyncGenerator"
            llm_response_generator: AsyncGenerator[str, None] = llm_ask_result
            
            # 确保 llm_response_generator 是 AsyncGenerator 类型
            if isinstance(llm_response_generator, AsyncGenerator):
                async for chunk in llm_response_generator:
                    full_response_content += chunk
                    # 每次有新chunk时，yield一个ChatAPIOutput的JSON字符串
                    chat_output = ChatAPIOutput(
                        sn=sn, # sn 不再从 run 方法获取，而是从 ChatAPIInput 中获取
                        userId=user_id,
                        sessionId=session_id,
                        name=None,
                        message=chunk, # 每次只发送当前chunk
                        media_url="",
                        code=200
                    )
                    yield f"data: "+ chat_output.model_dump_json() + "\n\n" # 添加换行符，确保每个JSON对象独立
                # 发送结束标志
                yield "data: [Done]\n\n"  # 结束标志

            logger.info(f"Qa response with LLM: {full_response_content}")

            # 4.保存模型响应到记忆。
            self.memory.update_memory("assistant", full_response_content)
            
        except Exception as e:
            logger.error(f"Qa error with LLM: {e}")
            self.state = AgentState.ERROR
            error_output = ChatAPIOutput(
                sn=sn, # sn 不再从 run 方法获取
                userId=user_id,
                sessionId=session_id,
                name=None,
                message=f"Qa error with LLM: {e}",
                media_url="",
                code=500
            )
            yield error_output.model_dump_json() + "\n" # 错误也以SSE形式发送
