
from typing import Dict
from app.memory.memory import Memory
from app.memory.session_storage import SessionStorage


class InMemorySessionStorage(SessionStorage):
    """内存存储实现，管理用户会话历史，使用单例模式确保全局唯一实例"""

    # 类变量，存储唯一实例
    _instance = None

    # 存储用户会话的字典
    user_sessions: Dict[str, Memory] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(InMemorySessionStorage, cls).__new__(cls)
            cls._instance.user_sessions = {}
        return cls._instance

    def get_session(self, user_id: str) -> Memory:
        """根据用户id获取历史会话"""
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = Memory()
        return self.user_sessions[user_id]

    def save_session(self, user_id: str, memory: Memory) -> None:
        """保存用户会话历史"""
        self.user_sessions[user_id] = memory

    def clead_session(self, user_id: str) -> None:
        """清除用户会话历史"""
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]