#!/bin/bash
# This script activates the virtual environment and runs the Ruyi AI Agent

APP_DIR="/opt/ruyi-ai-agent"
if [ ! -d "$APP_DIR" ]; then
    echo "Error: Application directory $APP_DIR does not exist."
    exit 1
fi
cd $APP_DIR

# 修改 mcp.json 中的路径
sed -i "s|/home/<USER>/|$HOME/|g" $HOME/.config/ruyi-ai/mcp.json

export PYTHONPATH=$APP_DIR/vendor:$PYTHONPATH

# 启动file search indexer
python3 app/tool/file_search_tool/indexer.py &


# 启动主程序
python3 main.py 




