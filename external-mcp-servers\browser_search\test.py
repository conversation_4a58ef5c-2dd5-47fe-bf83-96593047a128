from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from browser_use import Agent, <PERSON><PERSON><PERSON>
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from browser_use.controller.service import Controller
from Screenshot import Screenshot
from browser_use.controller.registry.service import Registry
from browser_use.controller.views import (
	ClickElementAction,
	DoneAction,
	ExtractPageContentAction,
	GoToUrlAction,
	InputTextAction,
	OpenTabAction,
	ScrollDownAction,
	SearchGoogleAction,
	SwitchTabAction,
)
import os
load_dotenv()

import asyncio
api_key = os.getenv('API_KEY')
base_url = os.getenv('Base_URL')
model = os.getenv('Model')

class RVBrowser(Browser):
    def __init__(self, headless: bool = False, keep_open: bool = False):
        self.headless = headless
        self.keep_open = keep_open
        self.MINIMUM_WAIT_TIME = 0.5
        self.MAXIMUM_WAIT_TIME = 5
        self._tab_cache: dict[str, TabInfo] = {}
        self._current_handle = None
        self._ob = Screenshot.Screenshot()
        # Initialize driver during construction
        self.driver: webdriver.Chrome | None = self._setup_webdriver()
        self._cached_state = Browser._update_state(self)
    def _setup_webdriver(self) -> webdriver.Chrome:
        """Sets up and returns a Selenium WebDriver instance with anti-detection measures."""
        try:
            # if webdriver is not starting, try to kill it or rm -rf ~/.wdm
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless=new')  # Updated headless argument

            # Essential automation and performance settings
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--window-size=1280,1024')
            chrome_options.add_argument('--disable-extensions')

            # Background process optimization
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-popup-blocking')

            # Additional stealth settings
            chrome_options.add_argument('--disable-infobars')
            # Much better when working in non-headless mode
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')

            # Initialize the Chrome driver with better error handling
            # service = ChromeService(ChromeDriverManager().install())
            # driver = webdriver.Chrome(service=service, options=chrome_options)
            custome_chrome = os.getenv('CUSTOM_CHROME')
            custome_chrome_driver = os.getenv('CUSTOM_CHROME_DRIVER')

            if custome_chrome:
                chrome_options.binary_location = custome_chrome
            if custome_chrome_driver:
                executable_path = custome_chrome_driver
                service = ChromeService(executable_path=executable_path)
            else:
                service = ChromeService()
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute stealth scripts
            driver.execute_cdp_cmd(
                'Page.addScriptToEvaluateOnNewDocument',
                {
                    'source': """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });
                    
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                    
                    window.chrome = {
                        runtime: {}
                    };
                    
                    Object.defineProperty(navigator, 'permissions', {
                        get: () => ({
                            query: Promise.resolve.bind(Promise)
                        })
                    });
                """
                },
            )
            return driver

        except Exception as e:
            # logger.error(f'Failed to initialize Chrome driver: {str(e)}')
            # Clean up any existing driver
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except Exception:
                    pass
            raise

class RVController(Controller):
    def __init__(self, keep_open: bool = False):
        self.browser = RVBrowser(keep_open=keep_open)
        self.registry = Registry()
        self._register_default_actions()
        self._register_bing()
    def _register_bing(self):
        """Register all default browser bing actions"""

        # Basic Navigation Actions
        @self.registry.action(
            'Search Bing', param_model=SearchGoogleAction, requires_browser=True
        )
        def search_bing(params: SearchGoogleAction, browser: Browser):
            driver = browser._get_driver()
            driver.get(f'https://www.bing.com/search?q={params.query}')
            browser.wait_for_page_load()

llm = ChatOpenAI(model=model, api_key=api_key, base_url=base_url)


async def main():
    
    controller = RVController()

    agent = Agent(
        task="使用 Bing 搜索中国最美的十个景点，按从南到北输出",
        llm=llm,
        controller=controller,
        use_vision=False,
    )
    result = await agent.run()
    print(result)

asyncio.run(main())

