from abc import abstractmethod
from typing import Optional
from app.agent.base_agent import BaseAgent


class ReActAgent(BaseAgent):
    """实现 ReAct（Reasoning + Acting）模式，扩展 BaseAgent。
    """

    @abstractmethod
    async def think(self, with_history: bool) -> bool:
        """
        决定是否需要执行动作，返回布尔值。
        """

    @abstractmethod
    async def act(self,user_id: str = "", session_id: Optional[str] = None) -> str:
        """
        执行动作，返回结果字符串。
        """

    async def step(self, with_history: bool) -> str:
        """
        Execute a single step: think and act.
        """
        # 按 ReAct 模式执行：
        # 调用 think 决定是否行动。
        should_act = await self.think(with_history)
        # 如果 should_act 为 False，返回停止信息。
        if not should_act:
            return "Thinking complete - no action needed"
        # 否则调用 act，返回动作结果。
        return await self.act()