"""Collection classes for managing multiple tools."""

from typing import Any, Dict, List
from app.logger import logger
from app.exception import ToolError
from app.tool.base_tool import BaseTool
from app.tool.tool_schema import ToolFailureResult, ToolResult

class ToolCollection:
    """管理 BaseTool 集合，支持迭代、调用、参数转换和动态添加。
    """

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, *tools: BaseTool):
        """实例初始化
        """
        self.tools = tools
        self.tool_map = {
            tool.name: tool
            for tool in tools
        }

    def to_params(self) -> List[Dict[str, Any]]:
        """将所有工具转为 OpenAI 工具调用格式（JSON）。"""
        return [
            tool.to_param()
            for tool in self.tools
        ]
    
    def __iter__(self):
        return iter(self.tools)

    def to_brief(self) -> str:
        """将所有工具转为简要描述（str）。"""
        briefs: str = ""
        for tool in self.tools:
            brief: Dict[str, str] = tool.to_brief()
            briefs += f'-- tool_name:{brief.get("name", "")}, \n-- tool_description:{brief.get("description", "")}'
        return briefs
        
    async def execute(self, *, name: str, tool_input: Dict[str, Any] = None) -> ToolResult:
        """异步调用指定工具，处理错误。"""
        # 按名称查找工具。
        tool = self.tool_map.get(name)
        if not tool:
            return ToolFailureResult(error = f"Tool {name} is invalid")
        try:
            # 执行工具，传入输入参数。
            result = await tool(**tool_input)
            return result
        except ToolError as e:
            # 捕获 ToolError，返回失败结果。
            return ToolFailureResult(error = e.message)
        
    async def execute_all(self) -> List[ToolResult]:
        """依次异步执行所有工具，收集结果或错误。"""
        results = []
        for tool in self.tools:
            try: 
                result = await tool()
                results.append(result)
            except ToolError as e:
                results.append(ToolFailureResult(error = e.message))
        return results
    
    def get_tool(self, name: str) -> BaseTool:
        return self.tool_map.get(name)

    def add_tool(self, tool: BaseTool):
        """Add a single tool to the collection.

        If a tool with the same name already exists, it will be skipped and a warning will be logged.
        """
        if tool.name in self.tool_map:
            logger.warning(f"Tool {tool.name} already exists in collection, skipping")
            return self

        self.tools += (tool,)
        self.tool_map[tool.name] = tool
        return self

    def add_tools(self, *tools: BaseTool):
        """Add multiple tools to the collection.

        If any tool has a name conflict with an existing tool, it will be skipped and a warning will be logged.
        """
        for tool in tools:
            self.add_tool(tool)
        return self