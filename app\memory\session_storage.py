
from abc import ABC, abstractmethod

from app.memory.memory import Memory


class SessionStorage(ABC):
    """抽象会话存储接口，用于管理用户的历史会话"""

    @abstractmethod
    def get_session(self, user_id: str) -> Memory:
        """根据用户id获取历史会话"""
        pass

    @abstractmethod
    def save_session(self, user_id: str, memory: Memory) -> None:
        """保存用户会话历史"""
        pass

    @abstractmethod
    def clead_session(self, user_id: str) -> None:
        """清除用户会话历史"""
        pass