
import argparse
import sys
from typing import Dict
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn
from app.api.api_schema import ErrorResponse
from app.exception import AgentError, ToolError
from app.logger import logger
from app.api.workflow_routes import workflow_router
from app.agent.base_agent import BaseAgent
from app.agent.qa_agent import QaAgent
from app.agent.os_agent import OSAgent
from app.agent.ruyi_agent import Ruyi
from app.agent.image_agent import ImageAgent
from app.agent.video_agent import VideoAgent
from app.config import config
from app.utils.event_manager import EventManager # 导入 EventManager

app = FastAPI()

expert_agents: Dict[str, BaseAgent] = {}

@app.on_event("startup")
async def startup_event():
    logger.info("初始化 MCP 服务器连接...")
    global expert_agents
    expert_agents = {
        "qa": QaAgent(),
        "os_agent": await OSAgent.create(
            connection_type="stdio",
            command=sys.executable,
            args=["-m", config.mcp_config.server_reference, "--transport", "stdio"],
        ),
        "ruyi_agent": await Ruyi.create(),
        "image_agent": ImageAgent(),
        "video_agent": VideoAgent()
   }
    logger.info("MCP 服务器连接初始化完成.")
    app.state.expert_agents = expert_agents
    app.state.event_manager = EventManager() # 初始化 EventManager

logger.info("注册 FastAPI 路由.")
app.include_router(workflow_router)



if __name__ == "__main__":
    logger.info("启动服务.")

    logger.info("实现全局异常处理​.")
    # 捕获自定义异常
    @app.exception_handler(ToolError)
    async def tool_error_handler(request: Request, ex: ToolError):
        return JSONResponse(
            status_code = ex.status_code,
            content = ErrorResponse(
                detail = ex.message,
                status_code = ex.status_code
            ).model_dump()
        )
    @app.exception_handler(AgentError)
    async def agent_error_handler(request: Request, ex: AgentError):
        # 对于AgentError的子类，如果未设置status_code则使用默认值
        if not hasattr(ex, 'status_code'):
            ex.status_code = 500  # 默认内部服务器错误
        return JSONResponse(
            status_code = ex.status_code,
            content = ErrorResponse(
                detail = ex.message,
                status_code = ex.status_code
            ).model_dump()
        )
    # 捕获 Pydantic 验证错误（可选）
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, ex: RequestValidationError):
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=ErrorResponse(
                detail=ex.errors(),  # 或自定义格式
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            ).model_dump()
        )
    # 捕获 ValueError 参数错误
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, ex: ValueError):
        return JSONResponse(
            status_code = status.HTTP_400_BAD_REQUEST,
            content = ErrorResponse(
                detail=str(ex),  # 使用 str(ex) 获取错误信息
                status_code=status.HTTP_400_BAD_REQUEST,
            ).model_dump()
        )
    # 捕获所有未处理的异常（兜底）
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, ex: Exception):
        logger.error(f"Unhandled exception: {str(ex)}", exc_info=True)  # 记录错误日志
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ErrorResponse(
                detail="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ).model_dump()
        )


    parser = argparse.ArgumentParser(
        prog="ruyi ai agent",
        description="基于 LLM 的多智能体",
    )
    

    parser.add_argument("--host", type=str, default="0.0.0.0")
    parser.add_argument("--port", type=int, default=7861)
    # 初始化消息
    args = parser.parse_args()

    logger.info("uvicorn运行.")
    uvicorn.run(app, host=args.host, port=args.port)
    logger.info("uvicorn退出.")