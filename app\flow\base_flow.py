# from abc import ABC, abstractmethod
# from typing import Dict, List, Optional, Union

# from pydantic import BaseModel

# from app.agent.base_agent import BaseAgent

# class BaseFlow(ABC, BaseModel):
#     """抽象基类，管理多个代理，提供代理选择和操作接口。"""
#     agents: Dict[str, BaseAgent] = None         # 代理字典，键为代理名称，值为 BaseAgent 实例。
#     primary_agent_key: Optional[str] = None     # 主代理的键，默认为第一个代理。

#     class Config:
#         arbitrary_types_allowed = True

#     def __init__(self, agents: Union[BaseAgent | List[BaseAgent] | Dict[str, BaseAgent]], **data):
#         if isinstance(agents, BaseAgent):
#             agents_dict = {"default": agents}
#         elif isinstance(agents, list):
#             agents_dict = {
#                 f"agent_{i}": agent
#                 for i, agent in enumerate(agents)
#             }
#         else:
#             agents_dict = agents
#         data["agents"] = agents_dict

#         primary_key = data.get("primary_agent_key")
#         if not primary_key and agents_dict:
#             primary_key = next(iter(agents_dict))
#             data["primary_agent_key"] = primary_key

#         super().__init__(**data)

#     @property
#     def primary_agent(self) -> Optional[BaseAgent]:
#         return self.agents.get(self.primary_agent_key)

#     def get_agent(self, key: str) -> Optional[BaseAgent]:
#         return self.agents.get(key)
    
#     def add_agent(self, key: str, agent: BaseAgent):
#         self.agents[key] = agent

#     @abstractmethod
#     async def execute(self, input_text: str) -> str:
#         """根据给定的输入执行流程"""