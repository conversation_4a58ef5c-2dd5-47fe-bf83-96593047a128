
import json
from typing import Any, List, Optional, Union

from pydantic import Field
from app.logger import logger
from app.agent.react_agent import ReActAgent
from app.exception import TokenLimitExceeded
from app.prompt.toolcall import NEXT_STEP_PROMPT, SYSTEM_PROMPT
from app.schema import TOOL_CHOICE_TYPE, AgentState, Message, ToolCall, ToolChoice
from app.tool.terminate_tool import Terminate
from app.tool.tool_collection import ToolCollection


class ToolCallAgent(ReActAgent):
    """
    ToolCallAgent 是一个具体实现的 ReAct 代理，专注于工具调用：
        ReAct 模式：通过 think（推理工具选择）和 act（执行工具）实现任务。
        工具集成：支持动态工具调用（ToolCollection），包括特殊工具（如 Terminate）。
        LLM 驱动：使用 LLM 决定工具调用，结合 system_prompt 和 next_step_prompt。
    应用：
        适合需要工具调用和多步骤推理的场景，如自动化任务、对话代理。
        自动化任务：
            通过 LLM 推理，选择并执行工具（如生成文本、终止任务）。
        对话系统：
            结合 system_prompt 和工具调用，处理用户请求。
        多步骤工作流：
            循环执行 think 和 act，支持复杂任务（如规划、调试）。
    """
    system_prompt: Optional[str] = SYSTEM_PROMPT
    next_step_prompt: Optional[str] = NEXT_STEP_PROMPT

    # 可用工具列表
    available_tools: ToolCollection = ToolCollection(
        Terminate()
    )
    # 特殊工具列表（如 Terminate），可能触发状态变更。
    special_tool_names: List[str] = Field(default_factory=lambda: [Terminate().name])
    # 控制工具调用行为（ToolChoice.AUTO、REQUIRED 或 NONE）。
    tool_choices: TOOL_CHOICE_TYPE = ToolChoice.AUTO # type: ignore
    
    # tool_calls 和 _current_base64_image 用于跟踪执行状态
    # 存储当前步骤的工具调用请求（ToolCall 列表）。
    tool_calls: List[ToolCall] = Field(default_factory=list)
    # 临时存储工具返回的 Base64 编码图像（如果有）。
    _current_base64_image: Optional[str] = None

    max_observe: Optional[Union[int, bool]] = None

    async def think(self, with_history: bool) -> bool:
        """决定是否需要执行工具调用。"""
        # 1.添加提示：
        # 如果 next_step_prompt 存在，添加到 messages 中，引导 LLM 推理。
        if self.next_step_prompt:
            self.memory.update_memory("user", self.next_step_prompt)

        self.system_msgs = [Message.system_message(self.system_prompt)] if self.system_prompt else None

        try:
            # 2.调用 LLM：
            # 使用 self.llm.ask_tool 请求 LLM，传入：
                # 当前消息历史（self.messages）。
                # 系统提示（system_prompt）。
                # 可用工具（available_tools.to_params()）。
                # 工具选择模式（tool_choices）。
            messages = self.memory.get_recent_messages(n=0) if with_history else self.memory.get_recent_messages(n=1)
            response = await self.llm.ask_tool(
                messages, 
                self.system_msgs,
                tools=self.available_tools.to_params(),
                tool_choice=self.tool_choices,
            )
        except ValueError:
            raise
        except Exception as e:
            # 错误处理：todo
            # 处理 TokenLimitExceeded（令牌限制错误），设置状态为 FINISHED。 
            if hasattr(e, "__cause__") and isinstance(e.__class__, TokenLimitExceeded):
                token_limit_error = e.__cause__
                logger.error(f"🚨 Token limit error (from RetryError): {token_limit_error}")
                self.memory.update_memory(
                    "assistant", 
                    content=f"Maximum token limit reached, cannot continue execution: {str(token_limit_error)}"
                )
                self.state = AgentState.FINISHED
                return False
            raise

        # 3.处理响应：
        # 存储工具调用（response.tool_calls）到 self.tool_calls。
        from app.schema import Function
        raw_tool_calls = response.tool_calls if response and response.tool_calls else []
        self.tool_calls = [
            ToolCall(
                id=tc.id, 
                type=tc.type, 
                function=Function(name=tc.function.name, arguments=tc.function.arguments)
            ) for tc in raw_tool_calls
        ]
        tool_calls = self.tool_calls
        # 记录响应内容（response.content）。
        content = response.content if response and response.content else ""
        # 日志记录 LLM 的思考和工具选择。
        logger.info(f"✨ {self.name}'s thoughts: {content}")
        logger.info(f"🛠️ {self.name} selected {len(tool_calls) if tool_calls else 0} tools to use")
        if tool_calls:
            logger.info(f"🧰 Tools being prepared: {[call.function.name for call in tool_calls]}")
            logger.info(f"🔧 Tool arguments: {[call.function.arguments for call in tool_calls]}")

        # 4.工具选择逻辑：
        try:
            if response is None:
                raise RuntimeError("No response received from the LLM")
            # ToolChoice.NONE：不应有工具调用，若有则警告，继续处理内容。
            if self.tool_choices == ToolChoice.NONE:
                if tool_calls:
                    logger.warning(
                        f"🤔 Hmm, {self.name} tried to use tools when they weren't available!"
                    )
                if content:
                    self.memory.update_memory("assistant", content=content)
                    return True
                return False
            # ToolChoice.REQUIRED：必须有工具调用，若无则返回 True（在 act 中处理错误）。
            if self.tool_calls:
                self.memory.add_message(Message.from_tool_calls(tool_calls=self.tool_calls, content=content))
            else:
                self.memory.update_memory("assistant", content=content)

            if self.tool_choices == ToolChoice.REQUIRED and not self.tool_calls:
                return True  # Will be handled in act()
            # ToolChoice.AUTO：如果有工具调用或内容，返回 True。
            if self.tool_choices == ToolChoice.AUTO and not self.tool_calls:
                return bool(content)
            return bool(self.tool_calls)
        except Exception as e:
            # 错误处理：
            # 其他异常记录错误并返回 False。
            logger.error(f"🚨 Oops! The {self.name}'s thinking process hit a snag: {e}")
            self.memory.add_message(
                Message.assistant_message(f"Error encountered while processing: {str(e)}")
            )
            return False    

    async def act(self,user_id: str = "", session_id: Optional[str] = None) -> Any:
        """执行动作，返回结果字符串。"""
        # 1.检查工具调用：
        # 如果 tool_calls 为空：
        # 若 tool_choices == REQUIRED，抛出错误。
        # 否则返回最后消息的内容。
        if not self.tool_calls:
            if self.tool_choices == ToolChoice.REQUIRED:
                raise ValueError("Tool calls required but none provided")
            # Return last message content if no tool calls
            return self.memory.get_recent_messages(n=1)[0].content or "No content or commands to execute"
        
        # 2.执行工具：
        # 遍历 self.tool_calls，调用 execute_tool 执行每个工具。
        results = []
        final_result = None
        for command in self.tool_calls:
            # 重置 _current_base64_image。
            self._current_base64_image = None
            args = json.loads(command.function.arguments or "{}")
            if self.event_manager:
                await self.event_manager.publish(user_id, "tool_call_start", {"tool_name": self.name, "tool_input": args}, session_id)
            
            result = await self.execute_tool(command)
            final_result = result
            result_output = str(result.output) if hasattr(result, "output") else str(result)


            if self.event_manager:
                await self.event_manager.publish(user_id, "tool_call_end", {"tool_name": self.name, "tool_output": result_output}, session_id)
            # 如果 max_observe 启用，截断结果。
            if self.max_observe:
                result_output = result_output[: self.max_observe]
            logger.info(
                f"🎯 Tool '{command.function.name}' completed its mission! Result: {result_output}"
            )

            # 3.记录结果：
            # 将工具执行结果添加到 memory（作为 tool_message）。
            # 收集所有结果，返回用 \n\n 拼接的字符串。
            self.memory.update_memory(
                "tool", 
                content=result_output, 
                base64_image=self._current_base64_image, 
                name=command.function.name, 
                tool_call_id=command.id
            )
            results.append(result_output)

            try:
                # 尝试将 result_output 解析为 JSON
                result_data = json.loads(result_output)
                if isinstance(result_data, dict) and result_data.get("status") == "waiting_for_human_input":
                    self.state = AgentState.WAITING_FOR_HUMAN_INPUT
                    logger.info(f"Agent state set to WAITING_FOR_HUMAN_INPUT by tool {command.function.name}")
                    return final_result
            except (json.JSONDecodeError, TypeError):
                # 如果 result_output 不是有效的 JSON 字符串，则忽略
                pass

        return "\n\n".join(results)
    
    async def execute_tool(self, command: ToolCall) -> Any:
        """执行单个工具调用（ToolCall），返回结果或错误信息。"""
        # 验证工具：
        # 检查 command 和工具名称的有效性。
        if not command or not command.function or not command.function.name:
            return "Error: Invalid command format"
        # 确保工具存在于 available_tools.tool_map。
        
        name = command.function.name
        if ":" in name:
            tool_name = name.split(":")[1]
            agent_name = name.split(":")[0]
        else:
            tool_name = name
            agent_name = "unknown"

        if tool_name not in self.available_tools.tool_map: # type: ignore
            return f"Error: Unknown tool '{name}'"
        
        try:
            # 1.解析参数：
            # 使用 json.loads 解析工具参数（command.function.arguments）。
            args = json.loads(command.function.arguments or "{}")
                    # 示例：在工具执行开始时发布事件


            # 2.执行工具：
            # 调用 self.available_tools.execute(name, tool_input=args)。
            logger.info(f"🔧 Activating tool: '{name}'...")
            result = await self.available_tools.execute(name=tool_name, tool_input=args)
            
            # 3.处理特殊工具（通过 _handle_special_tool）。
            await self._handle_special_tool(name=name, result=result)
            # 处理图像：
            # 如果结果包含 base64_image，存储到 _current_base64_image。
            if hasattr(result, "base64_image") and result.base64_image:
                self._current_base64_image = result.base64_image

            # 返回格式化的观察结果（Observed output of cmd ...）。
            # observation = (
            #     f"Observed output of cmd `{name}` executed:\n{str(result)}"
            #     if result
            #     else f"Cmd `{name}` completed with no output"
            # )
            return result
        
            # 4.错误处理：
            # JSON 解析错误：返回错误信息。
            # 其他异常：记录并返回错误。            
        except json.JSONDecodeError:
            error_msg = f"Error parsing arguments for {name}: Invalid JSON format"
            logger.error(
                f"📝 Oops! The arguments for '{name}' don't make sense - invalid JSON, arguments:{command.function.arguments}"
            )
            return f"Error: {error_msg}"
        except Exception as e:
            error_msg = f"⚠️ Tool '{name}' encountered a problem: {str(e)}"
            logger.exception(error_msg)
            return f"Error: {error_msg}"

    async def _handle_special_tool(self, name: str, result: Any, **kwargs):
        """Handle special tool execution and state changes"""
        if not self._is_special_tool(name):
            return

        if self._should_finish_execution(name=name, result=result, **kwargs):
            # Set agent state to finished
            logger.info(f"🏁 Special tool '{name}' has completed the task!")
            self.state = AgentState.FINISHED

    @staticmethod
    def _should_finish_execution(**kwargs) -> bool:
        """Determine if tool execution should finish the agent"""
        return True

    def _is_special_tool(self, name: str) -> bool:
        """Check if tool name is in special tools list"""
        return name.lower() in [n.lower() for n in self.special_tool_names]
