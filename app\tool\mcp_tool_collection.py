
from contextlib import AsyncExitStack
import os
from typing import Dict, List, Optional

from mcp import ClientSession, StdioServerParameters
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from app.logger import logger
from app.tool.mcp_tool import MCPClientTool
from app.tool.tool_collection import ToolCollection


class McpToolCollection(ToolCollection):
    """
    继承 ToolCollection，支持 SSE 和 stdio 连接，动态加载MCP 服务器工具。
    """

    sessions: Dict[str, ClientSession] = {}
    exit_stacks: Dict[str, AsyncExitStack] = {}
    name = "mcp_tool_collection"
    description: str = "MCP client tools for server interaction"

    def __init__(self):
        super().__init__()
        self.exit_stack = AsyncExitStack()  # 创建 AsyncExitStack 管理资源。

    async def connect_sse(self, server_url: str, server_id: str = "") -> None:
        """建立 SSE（Server-Sent Events）连接。"""
        # 验证 URL，断开现有连接。
        if not server_url:
            raise ValueError("Server URL is required.")
        
        logger.info(f"mcp_tool_collection, connect_sse, server_url: {server_url}, server_id: {server_id}")

        if self.session:
            await self.disconnect(server_id)
        # 创建 SSE 流，初始化会话。
        streams_context = sse_client(url=server_url)
        streams = await self.exit_stack.enter_async_context(streams_context)
        session_context = ClientSession(*streams)
        self.session = await self.exit_stack.enter_async_context(session_context)
        # 调用初始化方法加载工具。
        await self._initialize_and_list_tools(server_id)

    async def connect_stdio(
        self, command: str, args: List[str], server_id: str = ""
    ) -> None:
        """Connect to an MCP server using stdio transport."""
        if not command:
            raise ValueError("Server command is required.")

        logger.info(f"mcp_tool_collection, connect_stdio, command: {command}, args: {[arg for arg in args]}, server_id: {server_id}")

        server_id = server_id or command

        # Always ensure clean disconnection before new connection
        if server_id in self.sessions: # type: ignore
            await self.disconnect(server_id)

        exit_stack = AsyncExitStack()
        self.exit_stacks[server_id] = exit_stack

        server_params = StdioServerParameters(command=command, args=args)
        stdio_transport = await exit_stack.enter_async_context(
            stdio_client(server_params)
        )
        read, write = stdio_transport
        session = await exit_stack.enter_async_context(ClientSession(read, write))
        self.sessions[server_id] = session

        await self._initialize_and_list_tools(server_id)
    

    async def _initialize_and_list_tools(self, server_id: str) -> None:
        """动态加载服务器工具。"""
        session = self.sessions.get(server_id)
        if not session:
            raise RuntimeError(f"Session not initialized for server {server_id}")
        # 初始化会话，获取工具列表。

        await session.initialize()
        response = await session.list_tools() 
        self.tool_map = {}
        # 创建 MCPClientTool 实例。更新 tools 和 tool_map。
        for tool in response.tools:
            # 拆分 name 字段，格式为 agent_name:tool_name
            if ":" in tool.name:
                agent_name, tool_name = tool.name.split(":", 1)
            else:
                agent_name = "unknown"
                tool_name = tool.name

            # 创建 MCPClientTool 实例，使用拆分后的 tool_name 和 agent_name
            server_tool = MCPClientTool(
                name=tool.name, 
                description=tool.description, 
                parameters=tool.inputSchema, 
                session=session,
                agent_name=agent_name  # 传递拆分后的 agent_name
            )
            self.tool_map[tool.name] = server_tool
        self.tools = tuple(self.tool_map.values())
        logger.info(
            f"Connected to server with tools: {[tool.name for tool in response.tools]}"
        )

    async def disconnect(self,server_id: str = "") -> None:
        """清理连接和工具。"""
        """Disconnect from a specific MCP server or all servers if no server_id provided."""
        if server_id:
            if server_id in self.sessions:
                try:
                    exit_stack = self.exit_stacks.get(server_id)

                    # Close the exit stack which will handle session cleanup
                    if exit_stack:
                        try:
                            await exit_stack.aclose()
                        except RuntimeError as e:
                            if "cancel scope" in str(e).lower():
                                logger.warning(
                                    f"Cancel scope error during disconnect from {server_id}, continuing with cleanup: {e}"
                                )
                            else:
                                raise

                    # Clean up references
                    self.sessions.pop(server_id, None)
                    self.exit_stacks.pop(server_id, None)

                    # Remove tools associated with this server
                    self.tool_map = {
                        k: v
                        for k, v in self.tool_map.items()
                        if v.server_id != server_id
                    }
                    self.tools = tuple(self.tool_map.values())
                    logger.info(f"Disconnected from MCP server {server_id}")
                except Exception as e:
                    logger.error(f"Error disconnecting from server {server_id}: {e}")
        else:
            # Disconnect from all servers in a deterministic order
            for sid in sorted(list(self.sessions.keys())):
                await self.disconnect(sid)
            self.tool_map = {}
            self.tools = tuple()
            logger.info("Disconnected from all MCP servers")

async def main():
    # 动态加载 MCP 服务器工具
    mcp_tool_collection = McpToolCollection()
    try:
        # 连接到 MCPServer，使用正确路径
        server_path = os.path.join("mcpserver", "mcp_server.py")  # 相对路径
        await mcp_tool_collection.connect_stdio("python", [server_path, "--transport", "stdio"])
        # 检查工具是否加载
        if not mcp_tool_collection.tools:
            logger.error("No tools loaded from server")
            return
        # 执行工具
        result = await mcp_tool_collection.execute(name="Bash", tool_input={'command': "echo 'Hello, MCP!'", 'timeout': 30})
        logger.info(f"Tool result: {result}")
    finally:
        await mcp_tool_collection.disconnect()  # 显式清理

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
    