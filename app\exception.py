class ToolError(Exception):
    """Raised when a tool encounters an error."""
    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(message)

class AgentError(Exception):
    """Base exception for all Agent errors"""
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(message)

class TokenLimitExceeded(AgentError):
    """Exception raised when the token limit is exceeded"""
    def __init__(self, message: str = "Token limit exceeded", status_code: int = 413):
        super().__init__(message, status_code)

class ModelNotSupported(AgentError):
    """Model is not supporred"""
    def __init__(self, message: str = "Model not supported", status_code: int = 400):
        super().__init__(message, status_code)