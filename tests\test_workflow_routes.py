import pytest
import httpx
import json
import asyncio # 导入 asyncio 模块

@pytest.mark.asyncio
async def test_chat_completion():
    async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
        test_data = {
            "sn": "02022C000001XXXX",
            "userId": "user_082101",
            "sessionId": "session_082101",
            "content": [
                {"type": "text", "text": "帮我生成一张图片：一只可爱的猫咪在阳光下打盹，背景是一个温馨的客厅，有绿植和书架。"}
            ]
        }
        response = await client.post("/v2/chat", json=test_data)

        assert response.status_code == 200
        response_data = response.json()
        
        # 详细打印完整的响应数据
        print("\n=== 完整的响应数据 ===")
        for key, value in response_data.items():
            print(f"{key}: {value}")
        print("=====================")
        print("Debug - Complete response:", json.dumps(response_data, indent=2, ensure_ascii=False))
        # 基础字段验证
        assert "sn" in response_data
        assert "userId" in response_data
        assert "sessionId" in response_data
        assert "message" in response_data
        assert "code" in response_data
        assert "media_url" in response_data  # 明确验证 media_url 字段存在
        
        # 验证图片生成请求的 media_url
        if "media_url" in response_data:
            print(f"\nMedia URL found: {response_data['media_url']}")
            assert isinstance(response_data["media_url"], str)
            if response_data["media_url"]:  # 如果 media_url 非空
                assert response_data["media_url"].startswith("http")
        
        assert response_data["code"] == 200

@pytest.mark.asyncio
async def test_chat_completion_sse():
    """测试 SSE 流式响应"""
    async with httpx.AsyncClient(base_url="http://127.0.0.1:7861", timeout=None) as client: # 设置 timeout=None 以支持长连接
        test_data = {
            "sn": "02022C000001XXXX",
            "userId": "user_082101",
            "sessionId": "session_082101",
            "content": [
                {"type": "text", "text": "请给我讲一个关于人工智能的故事。"}
            ],
            "stream": True # 明确请求 SSE 流
        }
        
        full_response_content = ""
        async with client.stream("POST", "/v2/chat", json=test_data) as response:
            assert response.status_code == 200
            assert "text/event-stream" in response.headers["content-type"]

            async for chunk in response.aiter_bytes():
                decoded_chunk = chunk.decode("utf-8")
                full_response_content += decoded_chunk
                print(f"Received chunk: {decoded_chunk}") # 打印每个接收到的块

        # 解析完整的 SSE 响应
        events = [line.strip() for line in full_response_content.split("\n\n") if line.strip()]
        parsed_data = []
        for event in events:
            if event.startswith("data:"):
                try:
                    json_data = json.loads(event[len("data:"):].strip())
                    parsed_data.append(json_data)
                except json.JSONDecodeError as e:
                    print(f"JSON Decode Error: {e} in event: {event}")
                    continue

        assert len(parsed_data) > 0, "未接收到任何 SSE 数据事件"
        
        # 验证每个 SSE 事件的基本结构
        for data in parsed_data:
            assert "sn" in data
            assert "userId" in data
            assert "sessionId" in data
            assert "message" in data
            assert "code" in data
            assert data["code"] == 200
            assert isinstance(data["message"], str) # 确保 message 是字符串

        # 验证最终的 message 字段是否包含完整的故事内容
        final_message_parts = [data["message"] for data in parsed_data if "message" in data]
        final_message = "".join(final_message_parts)
        print(f"\nFinal assembled message: {final_message}")
        assert len(final_message) > 0, "最终消息内容为空"
        assert "人工智能" in final_message or "AI" in final_message # 验证内容相关性

            
# @pytest.mark.asyncio
# async def test_chat_completion_minimal():
#     """测试最小化输入参数的情况"""
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "content": [
#                 {"type": "text", "text": "你好"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)

#         assert response.status_code == 200
#         response_data = response.json()
#         assert response_data["code"] == 200
#         assert "message" in response_data

# @pytest.mark.asyncio
# async def test_chat_completion_with_media():
#     """测试返回带媒体URL的响应"""
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "sessionId": "session_082101",
#             "content": [
#                 {"type": "text", "text": "生成一张猫咪的图片"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)

#         assert response.status_code == 200
#         response_data = response.json()
#         assert response_data["code"] == 200
#         if "media_url" in response_data:
#             assert response_data["media_url"].startswith("http")

# @pytest.mark.asyncio
# async def test_adjust_volume_down():
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "sessionId": "session_082101",
#             "content": [
#                 {"type": "text", "text": "调低音量"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)
#         assert response.status_code == 200
#         response_data = response.json()
#         print(f'test_adjust_volume_down response: {response_data}')
#         assert response_data["code"] == 200

# @pytest.mark.asyncio
# async def test_adjust_volume_up():
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "sessionId": "session_082101",
#             "content": [
#                 {"type": "text", "text": "调高音量"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)
#         assert response.status_code == 200
#         response_data = response.json()
#         print(f'test_adjust_volume_up response: {response_data}')
#         assert response_data["code"] == 200

# @pytest.mark.asyncio
# async def test_adjust_brightness_down():
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "sessionId": "session_082101",
#             "content": [
#                 {"type": "text", "text": "调低屏幕亮度"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)
#         assert response.status_code == 200
#         response_data = response.json()
#         print(f'test_adjust_brightness_down response: {response_data}')
#         assert response_data["code"] == 200

# @pytest.mark.asyncio
# async def test_adjust_brightness_up():
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "sessionId": "session_082101",
#             "content": [
#                 {"type": "text", "text": "调高屏幕亮度"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)
#         assert response.status_code == 200
#         response_data = response.json()
#         print(f'test_adjust_brightness_up response: {response_data}')
#         assert response_data["code"] == 200

# @pytest.mark.asyncio
# async def test_open_browser():
#     async with httpx.AsyncClient(base_url="http://127.0.0.1:7861") as client:
#         test_data = {
#             "sn": "02022C000001XXXX",
#             "userId": "user_082101",
#             "sessionId": "session_082101",
#             "content": [
#                 {"type": "text", "text": "打开浏览器"}
#             ]
#         }
#         response = await client.post("/v2/chat", json=test_data)
#         assert response.status_code == 200
#         response_data = response.json()
#         print(f'test_open_browser response: {response_data}')
#         assert response_data["code"] == 200

@pytest.mark.asyncio
async def test_stream_agent_status():
    """测试 SSE 接口是否能实时接收 Agent 流程状态更新"""
    user_id = "test_user_for_stream_123"
    base_url = "http://127.0.0.1:7861"
    
    received_events = []
    
    async def consume_stream():
        """连接到 SSE 端点并消费事件"""
        try:
            async with httpx.AsyncClient(base_url=base_url, timeout=30) as client:
                async with client.stream("GET", f"/v2/stream/{user_id}") as response:
                    assert response.status_code == 200
                    assert "text/event-stream" in response.headers["content-type"]
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data:"):
                            data_str = line[len("data:"):].strip()
                            try:
                                data = json.loads(data_str)
                                received_events.append(data)
                                print(f"Received event: {data}")
                                # 如果收到了最终结果，可以提前结束
                                if data.get("event") == "final_result":
                                    break
                            except json.JSONDecodeError:
                                print(f"Failed to decode JSON: {data_str}")
        except httpx.ReadTimeout:
            print("Consumer timed out.")
        except Exception as e:
            print(f"Consumer error: {e}")

    async def produce_event():
        """通过调用 chat 接口来触发事件"""
        await asyncio.sleep(1) # 确保 consumer 已经连接上
        async with httpx.AsyncClient(base_url=base_url, timeout=30) as client:
            test_data = {
                "sn": "02022C000001XXXX",
                "userId": user_id,
                "sessionId": "session_stream_test",
                "content": [{"type": "text", "text": "你好，世界！"}]
            }
            response = await client.post("/v2/chat", json=test_data)
            assert response.status_code == 200

    # 并发执行消费者和生产者
    await asyncio.gather(
        consume_stream(),
        produce_event()
    )

    # 验证接收到的事件
    assert len(received_events) > 0, "没有接收到任何 SSE 事件"
    
    # 检查关键事件是否存在
    event_types = [event.get("event") for event in received_events]
    print(f"All received event types: {event_types}")
    
    assert "agent_routing_start" in event_types
    assert "agent_selection" in event_types
    assert "final_result" in event_types

    # 验证 final_result 事件的内容
    final_result_event = next((event for event in received_events if event.get("event") == "final_result"), None)
    assert final_result_event is not None
    assert "data" in final_result_event
    assert "message" in final_result_event["data"]
    assert final_result_event["data"]["message"] is not None
