from enum import Enum
import json
import time
from typing import AsyncGenerator, Dict, List, Optional, Union
from pydantic import Field
from app.agent.tool_call_agent import ToolCallAgent
from app.logger import logger
from app.agent.base_agent import BaseAgent
from app.llm.llm import LLM
from app.prompt.commander import EXECUTE_STEP_REQUEST, INIT_PLAN_SYSTEM_PROMPT, INIT_PLAN_USER_PROMPT
from app.schema import AgentState, Message, ToolChoice
from app.tool.planning_tool import PlanningTool
from app.tool.tool_collection import ToolCollection
from app.utils.event_manager import EventManager

class PlanStepStatus(str, Enum):
    """定义计划步骤状态，管理执行进度。"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"

    @classmethod
    def get_all_statuses(cls) -> list[str]:
        """返回所有状态。"""
        return [
            status.value
            for status in cls
        ]
    
    @classmethod
    def get_active_statuses(cls) -> list[str]:
        """返回未完成状态。"""
        return [cls.NOT_STARTED.value, cls.IN_PROGRESS.value]

    @classmethod
    def get_status_marks(cls) -> Dict[str, str]:
        """返回状态标记（如 [✓]、 [→]）。"""
        return {
            cls.COMPLETED.value: "[✓]",
            cls.IN_PROGRESS.value: "[→]",
            cls.BLOCKED.value: "[!]",
            cls.NOT_STARTED.value: "[ ]",
        }

class CommanderAgent(ToolCallAgent):
    """实现任务规划和执行，使用 LLM 和 PlanningTool 生成计划，协调代理执行步骤。"""
    llm: LLM = Field(default_factory=lambda: LLM())                     # 语言模型，驱动计划生成和总结。
    planning_tool: PlanningTool = Field(default_factory=PlanningTool)   # 管理计划的工具（创建、更新状态、获取状态）
    executor_keys: List[str] = Field(default_factory=list)              # 执行代理的键列表，默认所有代理。
    active_plan_id: str = Field(default_factory=lambda: f"plan_{int(time.time())}") # 当前计划 ID，基于时间戳。
    current_step_index: Optional[int] = None                            # 当前步骤索引。

    # 管理多个代理
    agents: Dict[str, BaseAgent] = Field(default_factory=dict) # 代理字典，键为代理名称，值为 BaseAgent 实例。
    primary_agent_key: Optional[str] = None     # 主代理的键，默认为第一个代理。

    name: str = "commander_agent"
    description: str = "An agent that implements sub task planning and execution, generates plans using LLM and Planning Tool, and coordinates expert agents to execute sub tasks."

    def __init__(
        self, agents: Union[BaseAgent, List[BaseAgent], Dict[str, BaseAgent]], **data
    ):
        planning_tool = data.get("planning_tool") or PlanningTool()
        data["planning_tool"] = planning_tool

        if "executors" in data:
            data["executor_keys"] = data.pop("executors")
        if "plan_id" in data:
            data["active_plan_id"] = data.pop("plan_id")

        if isinstance(agents, BaseAgent):
            agents_dict = {"default": agents}
        elif isinstance(agents, list):
            agents_dict = {
                f"agent_{i}": agent
                for i, agent in enumerate(agents)
            }
        else:
            agents_dict = agents
        data["agents"] = agents_dict

        primary_key = data.get("primary_agent_key")
        if not primary_key and agents_dict:
            primary_key = next(iter(agents_dict))
            data["primary_agent_key"] = primary_key

        super().__init__(**data)

        # 默认设置 executor_keys 为所有代理键。
        if not self.executor_keys:
            self.executor_keys = list(self.agents.keys())

        self.available_tools = ToolCollection(planning_tool)

    def get_executor(self, step_type: Optional[str] = None) -> BaseAgent:
        """动态分配代理，适应步骤类型（如 [SEARCH]、[CODE]）。"""
        # 如果 step_type（如 search、code）匹配代理键，选择该代理。
        if step_type and step_type in self.agents:
            return self.agents[step_type]
        # 否则，从 executor_keys 选择第一个可用代理。
        for key in self.executor_keys:
            if key in self.agents:
                return self.agents[key]
        # Fallback 到主代理。
        primary_agent = self.primary_agent
        if not primary_agent:
            raise ValueError("No primary agent is available.")
        return primary_agent

    async def run(self, 
                  user_id: str, 
                  request: Optional[str] = None, 
                  image_urls: Optional[List[str]] = None,
                  with_history: bool = True,
                  event_manager: Optional[EventManager] = None,
                  session_id: Optional[str] = None) -> Union[str, AsyncGenerator[str, None]]:
        """规划（生成计划）+ 执行（代理运行）。"""
        # 验证主代理存在。
        if not self.primary_agent:
            raise ValueError("No primary agent available")
        
        # 获取用户会话
        memory = self.session_storage.get_session(user_id=user_id)
        self.memory = memory  # 将当前用户的 memory 绑定到代理

        # 如果有输入，调用 _create_initial_plan 生成计划。
        if request:
            await self._create_initial_plan(request)
            # Verify plan was created successfully
            if self.active_plan_id not in self.planning_tool.plans:
                logger.error(
                    f"Plan creation failed. Plan ID {self.active_plan_id} not found in planning tool."
                )
                yield f"Failed to create plan for: {request}"
        # 循环：
        # result = ""
        while True:
            # 获取当前步骤（_get_current_step_info）。
            self.current_step_index, step_info = await self._get_current_step_info()
            # 如果无步骤，调用 _finalize_plan 结束。
            if self.current_step_index is None:
                finalize_result = await self._finalize_plan(user_id)
                if isinstance(finalize_result, AsyncGenerator):
                    async for item in finalize_result:
                        yield item
                else:
                    yield finalize_result
                break
            # 选择执行代理（get_executor），运行步骤（_execute_step）。
            assert step_info is not None  # Ensure step_info is not None
            step_type = step_info.get("type") if step_info else None
            executor = self.get_executor(step_type)
            step_result = await self._execute_step(executor, user_id, step_info)
            if isinstance(step_result,AsyncGenerator):
                async for item in step_result:
                    yield item
            else:
                yield step_result
                
            # 检查代理状态，若 FINISHED，退出。
            if hasattr(executor, "state") and executor.state == AgentState.FINISHED:
                break
        #return result
        # 返回结果（步骤输出 + 总结）。
        
    async def _create_initial_plan(self, request: str) -> None:
        """智能生成计划，fallback 确保健壮性。todo steps中包含agent key"""
        # 1.使用 LLM 和 PlanningTool 生成计划
        # Create a system message for plan creation
        agent_info = ""
        for name, agent in self.agents.items():
            if isinstance(agent, ToolCallAgent):
                agent_info += f"- {name}: {agent.description}, \n可用工具: \n{agent.available_tools.to_brief()}"
                agent_info += "\n"
            elif isinstance(agent, BaseAgent):
                agent_info += f"- {name}: {agent.description}"
                agent_info += "\n"
        system_message = Message.system_message(INIT_PLAN_SYSTEM_PROMPT.format(agent_info=agent_info))
        # Create a user message with the request
        user_message = Message.user_message(INIT_PLAN_USER_PROMPT.format(request=request))
        response = await self.llm.ask_tool(
            messages=[user_message],
            system_msgs=[system_message],
            tools=[self.planning_tool.to_param()],
            tool_choice=ToolChoice.AUTO,
        )
        # 2.调用 planning_tool.execute。
        if response and response.tool_calls:
            for tool_call in response.tool_calls:
                if tool_call.function.name == "planning":
                    # Parse the arguments
                    args = tool_call.function.arguments
                    if isinstance(args, str):
                        try:
                            args = json.loads(args)
                        except json.JSONDecodeError:
                            logger.error(f"Failed to parse tool arguments: {args}")
                            continue
                    # Ensure plan_id is set correctly and execute the tool
                    args["plan_id"] = self.active_plan_id
                    # Execute the tool via ToolCollection instead of directly
                    result = await self.planning_tool.execute(**args)
                    logger.info(f"Plan creation result: {str(result)}")
                    return
        # 3.若失败，创建默认计划（3 步：分析、执行、验证）。
        # If execution reached here, create a default plan
        logger.warning("Creating default plan")
        # Create default plan using the ToolCollection
        await self.planning_tool.execute(
            **{
                "command": "create",
                "plan_id": self.active_plan_id,
                "title": f"Plan for: {request[:50]}{'...' if len(request) > 50 else ''}",
                "steps": ["Analyze request", "Execute task", "Verify results"],
            }
        )

    async def _get_current_step_info(self) -> tuple[Optional[int], Optional[dict]]:
        """动态跟踪计划进度，选择下一个执行步骤。"""
        if (
            not self.active_plan_id
            or self.active_plan_id not in self.planning_tool.plans
        ):
            logger.error(f"Plan with ID {self.active_plan_id} not found")
            return None, None
        try:
            # 从 planning_tool.plans 获取计划，遍历步骤。
            plan_data = self.planning_tool.plans[self.active_plan_id]
            steps = plan_data.get("steps", [])
            step_statuses = plan_data.get("step_statuses", [])
            for i, step in enumerate(steps):
                if i > len(step_statuses):
                    status = PlanStepStatus.NOT_STARTED.value
                else:
                    status = step_statuses[i]

                # 找到第一个未完成步骤（NOT_STARTED 或 IN_PROGRESS）。
                if status in PlanStepStatus.get_active_statuses():
                    step_info = {"text": step}
                    # 解析步骤文本，提取 type 和 recommended_solution
                    import re
                    type_match = re.search(r"\[([A-Za-z]+)\]\s*(.+?)(?:\s*=>\s*(.+))?$", step)
                    if type_match:
                        step_info["type"] = type_match.group(1).lower()
                        step_info["subtask_request"] = type_match.group(2) or ""
                        step_info["recommended_solution"] = type_match.group(3) or ""
                    # Mark current step as in_progress
                    try:
                        await self.planning_tool.execute(
                            command="mark_step",
                            plan_id=self.active_plan_id,
                            step_index=i,
                            step_status=PlanStepStatus.IN_PROGRESS.value,
                        )
                    except Exception as e:
                        logger.warning(f"Error marking step as in_progress: {e}")
                        # Update step status directly if needed
                        if i < len(step_statuses):
                            step_statuses[i] = PlanStepStatus.IN_PROGRESS.value
                        else:
                            while len(step_statuses) < i:
                                step_statuses.append(PlanStepStatus.NOT_STARTED.value)
                            step_statuses.append(PlanStepStatus.IN_PROGRESS.value)

                        plan_data["step_statuses"] = step_statuses

                    return i, step_info
            # 若无活跃步骤，返回 (None, None)。
            return None, None
        except Exception as e:
            logger.warning(f"Error finding current step index: {e}")
            return None, None

    async def _execute_step(self, executor: BaseAgent, user_id:str, step_info: dict) -> Union[str, AsyncGenerator[str, None]]:
        """提供上下文驱动代理执行，确保步骤清晰。"""
        # 生成提示，包含计划状态和步骤描述。
        plan_status = await self._get_plan_text()
        step_text = step_info.get("text", f"Step {self.current_step_index}")
        subtask_request = step_info.get("subtask_request", "")
        recommended_solution = step_info.get("recommended_solution", "")

        # 调用代理的 run 方法执行步骤。
        step_prompt = EXECUTE_STEP_REQUEST.format(
            plan_status=plan_status,
            current_step_index=self.current_step_index,
            subtask_request=subtask_request,
            recommended_solution=recommended_solution
        )
        try:
            step_result_generator = executor.run(user_id, step_prompt, None, False)
            
            # Handle both async generator and coroutine
            if isinstance(step_result_generator, AsyncGenerator):
                step_result = step_result_generator
            else:
                step_result = await step_result_generator

            # 标记步骤完成（_mark_step_completed）。
            await self._mark_step_completed()
            return step_result
        except Exception as e:
            logger.error(f"Error executing step {self.current_step_index}: {e}")
            return f"Error executing step {self.current_step_index}: {str(e)}"
    
    async def _mark_step_completed(self) -> None:
        """Mark the current step as completed."""
        if self.current_step_index is None:
            return

        try:
            # Mark the step as completed
            await self.planning_tool.execute(
                command="mark_step",
                plan_id=self.active_plan_id,
                step_index=self.current_step_index,
                step_status=PlanStepStatus.COMPLETED.value,
            )
            logger.info(
                f"Marked step {self.current_step_index} as completed in plan {self.active_plan_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to update plan status: {e}")
            # Update step status directly in planning tool storage
            if self.active_plan_id in self.planning_tool.plans:
                plan_data = self.planning_tool.plans[self.active_plan_id]
                step_statuses = plan_data.get("step_statuses", [])

                # Ensure the step_statuses list is long enough
                while len(step_statuses) <= self.current_step_index:
                    step_statuses.append(PlanStepStatus.NOT_STARTED.value)

                # Update the status
                step_statuses[self.current_step_index] = PlanStepStatus.COMPLETED.value
                plan_data["step_statuses"] = step_statuses

    async def _get_plan_text(self) -> str:
        # 从 planning_tool 获取格式化计划，或从存储生成（_generate_plan_text_from_storage）。
        try:
             result = await self.planning_tool.execute(
                 command="get", plan_id=self.active_plan_id
             )
             return result.output if hasattr(result, "output") else str(result)
        except Exception as e:
            logger.error(f"Error getting plan: {e}")
            return self._generate_plan_text_from_storage()
        
    def _generate_plan_text_from_storage(self) -> str:
        """直接从 planning_tool.plans 生成计划文本，包含标题、进度和步骤状态。"""
        try:
            if self.active_plan_id not in self.planning_tool.plans:
                return f"Error: Plan with ID {self.active_plan_id} not found"
            plan_data = self.planning_tool.plans[self.active_plan_id]
            title = plan_data.get("title", "Untitled Plan")
            steps = plan_data.get("steps", [])
            step_statuses = plan_data.get("step_statuses", [])
            step_notes = plan_data.get("step_notes", [])

            # Ensure step_statuses and step_notes match the number of steps
            while len(step_statuses) < len(steps):
                step_statuses.append(PlanStepStatus.NOT_STARTED.value)
            while len(step_notes) < len(steps):
                step_notes.append("")      

            # Count steps by status
            status_counts = {status: 0 for status in PlanStepStatus.get_all_statuses()}

            for status in step_statuses:
                if status in status_counts:
                    status_counts[status] += 1

            completed = status_counts[PlanStepStatus.COMPLETED.value]
            total = len(steps)
            progress = (completed / total) * 100 if total > 0 else 0        

            plan_text = f"Plan: {title} (ID: {self.active_plan_id})\n"
            plan_text += "=" * len(plan_text) + "\n\n"

            plan_text += (
                f"Progress: {completed}/{total} steps completed ({progress:.1f}%)\n"
            )
            plan_text += f"Status: {status_counts[PlanStepStatus.COMPLETED.value]} completed, {status_counts[PlanStepStatus.IN_PROGRESS.value]} in progress, "
            plan_text += f"{status_counts[PlanStepStatus.BLOCKED.value]} blocked, {status_counts[PlanStepStatus.NOT_STARTED.value]} not started\n\n"
            plan_text += "Steps:\n"

            status_marks = PlanStepStatus.get_status_marks()

            for i, (step, status, notes) in enumerate(
                zip(steps, step_statuses, step_notes)
            ):
                # Use status marks to indicate step status
                status_mark = status_marks.get(
                    status, status_marks[PlanStepStatus.NOT_STARTED.value]
                )

                plan_text += f"{i}. {status_mark} {step}\n"
                if notes:
                    plan_text += f"   Notes: {notes}\n"

            return plan_text
        except Exception as e:
            logger.error(f"Error generating plan text from storage: {e}")
            return f"Error: Unable to retrieve plan with ID {self.active_plan_id}"

    '''
    Fallback 机制
    1.Fallback 逻辑：
    (1)第一层（LLM 失败）：
        如果 llm.ask 抛出异常（Exception as e），记录错误。
        尝试使用主代理（self.primary_agent）：
            创建提示（summary_prompt），包含计划状态。
            调用 agent.run(summary_prompt) 生成总结。
            返回 "Plan completed:\n\n{agent summary}".
    (2)第二层（代理失败）：
        如果代理运行失败（Exception as e2），记录错误。
        返回默认消息："Plan completed. Error generating summary."
    2.Fallback 的目的：
        确保即使 LLM 或代理失败，方法仍返回有效结果。
        提供降级方案，增强健壮性。
    '''
    async def _finalize_plan(self, user_id:str) -> Union[str, AsyncGenerator[str, None]]: 
        """提供任务完成后的智能总结。todo:总结结果 """
        # 获取最终计划状态。
        plan_text = await self._get_plan_text()
        # 使用 LLM 生成总结，fallback 到主代理。
        try:
            system_message = Message.system_message(
                "You are a planning assistant. Your task is to summarize the completed plan."
            )

            user_message = Message.user_message(
                f"The plan has been completed. Here is the final plan status:\n\n{plan_text}\n\nPlease provide a summary of what was accomplished and any final thoughts."
            )

            response = await self.llm.ask(
                messages=[user_message], system_msgs=[system_message],
            )
            # 返回总结结果。
            return response
        except Exception as e:
            logger.error(f"Error finalizing plan with LLM: {e}")
            # Fallback to using an agent for the summary
            try:
                agent = self.primary_agent
                if agent:
                    summary_prompt = f"""
                    The plan has been completed. Here is the final plan status:

                    {plan_text}

                    Please provide a summary of what was accomplished and any final thoughts.
                    """
                    summary = await agent.run(user_id=user_id, request=summary_prompt, with_history=True)
                    return f"Plan completed:\n\n{summary}"
                else:
                    logger.error("No primary agent available to finalize the plan.")
                    return "Plan completed. Error generating summary: No primary agent."
            except Exception as e2:
                logger.error(f"Error finalizing plan with agent: {e2}")
                return "Plan completed. Error generating summary."
            
    @property
    def primary_agent(self) -> Optional[BaseAgent]:
        if not self.primary_agent_key:
            return None
        return self.agents.get(self.primary_agent_key)

    def get_agent(self, key: str) -> Optional[BaseAgent]:
        return self.agents.get(key)
    
    def add_agent(self, key: str, agent: BaseAgent):
        self.agents[key] = agent
