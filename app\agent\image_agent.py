import json
import requests
from typing import List, Optional, Union, Dict, Any, cast

from pydantic import model_validator
from app.llm.llm import LLM
from app.logger import logger
from app.agent.base_agent import BaseAgent
from app.prompt.image import USER_PROMPT, SYSTEM_PROMPT
from app.schema import AgentState, Message

from app.config import config

class ImageAgent(BaseAgent):
    """
    AI图像生成助手，根据用户描述生成图像。
    """

    name: str = "image_agent"

    description: str = """你是一个AI图像生成助手，根据用户描述生成图像。功能包括：
    1. 根据用户的文本描述生成高质量的图像。
    2. 支持多种图像风格和元素描述。
    """

    system_prompt: Optional[str] = SYSTEM_PROMPT

    user_prompt: str = USER_PROMPT

    @model_validator(mode="after")
    def initialize_agent(self) -> "ImageAgent":
        # 使用一个适合生成文本Prompt的模型，不一定是vision模型
        self.llm = LLM(config_name="default")  # 假设 'default' 配置使用文本模型
        return self

    async def run(
        self,
        user_id: str,
        request: Optional[str] = None,
        image_urls: Optional[List[str]] = None,
        with_history: bool = True,
        event_manager: Optional[Any] = None,
        session_id: Optional[str] = None,
    ) -> str:
        self.event_manager = event_manager
        if not request:
            return json.dumps({
                "message": "请提供您想要生成的图像描述。",
                "media_url": None
            }, ensure_ascii=False)

        # 1. 获取用户会话
        if with_history:
            memory = self.session_storage.get_session(user_id=user_id)
            self.memory = memory
            # 保存用户的请求到记忆中
            self.memory.update_memory("user", request)

        try:
            # 2. 准备发送给 LLM 的消息
            user_message = Message.user_message(content=self.user_prompt.format(request=request)).to_dict()
            messages = cast(List[Union[Dict[str, Any], Message]], [user_message])
            
            # 只有当 system_prompt 不为 None 时才添加系统消息
            system_msgs = None
            if self.system_prompt:
                system_message = Message.system_message(content=self.system_prompt).to_dict()
                system_msgs = cast(List[Union[Dict[str, Any], Message]], [system_message])

            # 3. 调用 LLM 生成图像 Prompt
            logger.info(f"Calling LLM to generate image prompt for request: {request}")
            if self.event_manager:
                await self.event_manager.publish(
                    user_id,
                    "thought",
                    {"agent_name": self.name, "thought_process": "正在为您生成合适的图像描述提示词..."},
                    session_id
                )
            image_prompt = await self.llm.ask(
                messages=messages,
                system_msgs=system_msgs,
                stream=False,  # 不需要流式输出
            )
            logger.info(f"Generated image prompt: {image_prompt}")

            if not image_prompt:
                return json.dumps({
                    "message": "未能生成有效的图像Prompt，请尝试更具体的描述。",
                    "media_url": None
                }, ensure_ascii=False)            # 4. 调用图像生成 API (作为工具调用)
            logger.info(f"Calling image generation API with prompt: {image_prompt}")
            
            # 获取 ModelScope 配置
            image_generation_config = config.image_generation
            if not image_generation_config:
                raise ValueError("ModelScope configuration not found")

            payload = {
                'model': image_generation_config.default_model,
                'prompt': image_prompt
            }
            headers = {
                'Authorization': f'Bearer {image_generation_config.api_key}',
                'Content-Type': 'application/json'
            }
            
            tool_name = "modelscope_image_generation"
            if self.event_manager:
                await self.event_manager.publish(
                    user_id,
                    "tool_call_start",
                    {"agent_name": self.name, "tool_name": tool_name, "tool_input": {"prompt": image_prompt}},
                    session_id
                )
            
            response = requests.post(
                image_generation_config.base_url,
                data=json.dumps(payload, ensure_ascii=False).encode('utf-8'),
                headers=headers
            )
            response.raise_for_status()
            response_data = response.json()

            if self.event_manager:
                await self.event_manager.publish(
                    user_id,
                    "tool_call_end",
                    {"agent_name": self.name, "tool_name": tool_name, "tool_output": response_data},
                    session_id
                )

            if 'images' not in response_data or not response_data['images']:
                logger.error(f"Image generation API returned no images: {response_data}")
                return json.dumps({
                    "message": "图像生成失败，API未返回图像数据。",
                    "media_url": None
                }, ensure_ascii=False)

            image_url = response_data['images'][0]['url']
            logger.info(f"Image generated at URL: {image_url}")

            return json.dumps({
                "message": "图片生成成功",
                "media_url": image_url
            }, ensure_ascii=False)

        except requests.exceptions.RequestException as e:
            error_message = f"调用图像生成API失败: {e}"
            logger.error(f"Image generation API request failed: {e}")
            self.state = AgentState.ERROR
            if self.event_manager:
                await self.event_manager.publish(user_id, "agent_error", {"error_message": error_message}, session_id)
            return json.dumps({"message": error_message, "media_url": None}, ensure_ascii=False)
        except Exception as e:
            error_message = f"图像生成过程中发生错误: {e}"
            logger.error(f"ImageAgent run error: {e}")
            self.state = AgentState.ERROR
            if self.event_manager:
                await self.event_manager.publish(user_id, "agent_error", {"error_message": error_message}, session_id)
            return json.dumps({"message": error_message, "media_url": None}, ensure_ascii=False)
