import os
import time
import threading
import sqlite3
from typing import Set, List, Dict, Optional
from pathlib import Path
from queue import Queue, Empty
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent
import fnmatch

from app.logger import logger
from app.config import config
from .indexer import get_file_metadata, create_table, DATABASE_NAME


class FileChangeEvent:
    """Represents a file change event"""
    def __init__(self, event_type: str, file_path: str, is_directory: bool = False):
        self.event_type = event_type  # 'created', 'modified', 'deleted', 'moved'
        self.file_path = file_path
        self.is_directory = is_directory
        self.timestamp = time.time()

    def __repr__(self):
        return f"FileChangeEvent({self.event_type}, {self.file_path}, {self.is_directory})"


class FileWatcherEventHandler(FileSystemEventHandler):
    """Handles file system events and queues them for processing"""
    
    def __init__(self, event_queue: Queue, ignore_patterns: List[str]):
        super().__init__()
        self.event_queue = event_queue
        self.ignore_patterns = ignore_patterns
        
    def _should_ignore(self, file_path: str) -> bool:
        """Check if file should be ignored based on patterns"""
        for pattern in self.ignore_patterns:
            if fnmatch.fnmatch(file_path, pattern) or fnmatch.fnmatch(os.path.basename(file_path), pattern):
                return True
        return False
    
    def on_created(self, event: FileSystemEvent):
        if not self._should_ignore(event.src_path):
            self.event_queue.put(FileChangeEvent('created', event.src_path, event.is_directory))
    
    def on_modified(self, event: FileSystemEvent):
        if not self._should_ignore(event.src_path):
            self.event_queue.put(FileChangeEvent('modified', event.src_path, event.is_directory))
    
    def on_deleted(self, event: FileSystemEvent):
        if not self._should_ignore(event.src_path):
            self.event_queue.put(FileChangeEvent('deleted', event.src_path, event.is_directory))
    
    def on_moved(self, event: FileSystemEvent):
        if hasattr(event, 'dest_path'):
            if not self._should_ignore(event.src_path):
                self.event_queue.put(FileChangeEvent('deleted', event.src_path, event.is_directory))
            if not self._should_ignore(event.dest_path):
                self.event_queue.put(FileChangeEvent('created', event.dest_path, event.is_directory))


home_dir = os.environ.get("HOME", os.path.expanduser("~"))

class FileWatcher:
    """Main file watcher class that monitors file changes and updates the database"""
    
    def __init__(self):
        self.observer = Observer()
        self.event_queue = Queue()
        self.processing_thread = None
        self.is_running = False
        self.db_path = os.path.join(os.path.join(home_dir, ".local/share/file_search"), DATABASE_NAME)
        
        # Load configuration
        self.config = config.file_watcher_config
        if not self.config:
            logger.warning("File watcher configuration not found, using defaults")
            # Create default configuration
            from app.config import FileWatcherSettings
            self.config = FileWatcherSettings()
        
        self.watch_paths = self.config.watch_paths or [os.path.expanduser("~")]
        self.ignore_patterns = self.config.ignore_patterns or []
        self.batch_size = self.config.batch_size
        self.batch_timeout = self.config.batch_timeout
        self.update_interval = self.config.update_interval
        self.max_file_size = self.config.max_file_size
        
        logger.info(f"File watcher initialized with paths: {self.watch_paths}")
        logger.info(f"Ignore patterns: {self.ignore_patterns}")
    
    def _get_db_connection(self) -> sqlite3.Connection:
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        create_table(conn)
        return conn
    
    def _process_file_event(self, event: FileChangeEvent, conn: sqlite3.Connection):
        """Process a single file event"""
        cursor = conn.cursor()
        
        try:
            if event.event_type == 'deleted':
                # Remove from database
                cursor.execute("DELETE FROM files WHERE filepath = ?", (event.file_path,))
                logger.debug(f"Deleted from DB: {event.file_path}")
                
            elif event.event_type in ['created', 'modified']:
                # Check if file still exists (might have been deleted quickly)
                if not os.path.exists(event.file_path):
                    return
                
                # Skip if file is too large
                try:
                    file_size = os.path.getsize(event.file_path)
                    if file_size > self.max_file_size:
                        logger.debug(f"Skipping large file: {event.file_path} ({file_size} bytes)")
                        return
                except OSError:
                    return
                
                # Get file metadata
                try:
                    metadata = get_file_metadata(event.file_path)
                except Exception as e:
                    logger.error(f"Error getting metadata for {event.file_path}: {e}")
                    return
                
                # Check if file exists in database
                cursor.execute("SELECT last_modified FROM files WHERE filepath = ?", (event.file_path,))
                result = cursor.fetchone()
                
                if result is None:
                    # New file, insert it
                    cursor.execute('''
                        INSERT INTO files 
                        (filepath, filename, extension, size, last_modified, created_time, is_dir, text_content, modality)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        metadata['filepath'],
                        metadata['filename'],
                        metadata['extension'],
                        metadata['size'],
                        metadata['last_modified'],
                        metadata['created_time'],
                        metadata['is_dir'],
                        metadata['text_content'],
                        metadata['modality']
                    ))
                    logger.debug(f"Added to DB: {event.file_path}")
                else:
                    # File exists, update if modified
                    db_last_modified = result[0]
                    if metadata['last_modified'] > db_last_modified:
                        cursor.execute('''
                            UPDATE files SET
                            filename = ?,
                            extension = ?,
                            size = ?,
                            last_modified = ?,
                            created_time = ?,
                            is_dir = ?,
                            text_content = ?,
                            modality = ?
                            WHERE filepath = ?
                        ''', (
                            metadata['filename'],
                            metadata['extension'],
                            metadata['size'],
                            metadata['last_modified'],
                            metadata['created_time'],
                            metadata['is_dir'],
                            metadata['text_content'],
                            metadata['modality'],
                            metadata['filepath']
                        ))
                        logger.debug(f"Updated in DB: {event.file_path}")
                        
        except Exception as e:
            logger.error(f"Error processing event {event}: {e}")
    
    def _process_events(self):
        """Process events from the queue in batches"""
        conn = self._get_db_connection()
        
        try:
            while self.is_running:
                events = []
                start_time = time.time()
                
                # Collect events for batch processing
                while len(events) < self.batch_size and (time.time() - start_time) < self.batch_timeout:
                    try:
                        event = self.event_queue.get(timeout=0.1)
                        events.append(event)
                    except Empty:
                        continue
                
                if events:
                    logger.info(f"Processing batch of {len(events)} file events")
                    
                    # Process events
                    for event in events:
                        self._process_file_event(event, conn)
                    
                    # Commit changes
                    conn.commit()
                    logger.debug(f"Committed {len(events)} changes to database")
                
                # Sleep to avoid excessive CPU usage
                time.sleep(self.update_interval)
                
        except Exception as e:
            logger.error(f"Error in event processing thread: {e}")
        finally:
            conn.close()
    
    def start(self):
        """Start the file watcher"""
        if self.is_running:
            logger.warning("File watcher is already running")
            return
        
        if not self.config.enabled:
            logger.info("File watcher is disabled in configuration")
            return
        
        logger.info("Starting file watcher...")
        self.is_running = True
        
        # Create event handler
        event_handler = FileWatcherEventHandler(self.event_queue, self.ignore_patterns)
        
        # Add watchers for each path
        for watch_path in self.watch_paths:
            if os.path.exists(watch_path):
                self.observer.schedule(event_handler, watch_path, recursive=True)
                logger.info(f"Watching path: {watch_path}")
            else:
                logger.warning(f"Watch path does not exist: {watch_path}")
        
        # Start observer
        self.observer.start()
        
        # Start processing thread
        self.processing_thread = threading.Thread(target=self._process_events, daemon=True)
        self.processing_thread.start()
        
        logger.info("File watcher started successfully")
    
    def stop(self):
        """Stop the file watcher"""
        if not self.is_running:
            return
        
        logger.info("Stopping file watcher...")
        self.is_running = False
        
        # Stop observer
        self.observer.stop()
        self.observer.join()
        
        # Wait for processing thread to finish
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5)
        
        logger.info("File watcher stopped")
    
    def get_status(self) -> Dict:
        """Get current status of the file watcher"""
        return {
            'is_running': self.is_running,
            'watch_paths': self.watch_paths,
            'queue_size': self.event_queue.qsize(),
            'observer_alive': self.observer.is_alive() if self.is_running else False,
            'processing_thread_alive': self.processing_thread.is_alive() if self.processing_thread else False
        }


# Global file watcher instance
_file_watcher_instance = None
_watcher_lock = threading.Lock()


def get_file_watcher() -> FileWatcher:
    """Get the global file watcher instance"""
    global _file_watcher_instance
    
    if _file_watcher_instance is None:
        with _watcher_lock:
            if _file_watcher_instance is None:
                _file_watcher_instance = FileWatcher()
    
    return _file_watcher_instance


def start_file_watcher():
    """Start the global file watcher"""
    watcher = get_file_watcher()
    watcher.start()


def stop_file_watcher():
    """Stop the global file watcher"""
    global _file_watcher_instance
    if _file_watcher_instance:
        _file_watcher_instance.stop()


def get_watcher_status() -> Dict:
    """Get status of the global file watcher"""
    global _file_watcher_instance
    if _file_watcher_instance:
        return _file_watcher_instance.get_status()
    return {'is_running': False}


if __name__ == '__main__':
    # Test the file watcher
    watcher = FileWatcher()
    try:
        watcher.start()
        logger.info("File watcher test started. Press Ctrl+C to stop.")
        while True:
            status = watcher.get_status()
            logger.info(f"Status: {status}")
            time.sleep(10)
    except KeyboardInterrupt:
        logger.info("Stopping file watcher...")
        watcher.stop()
