{"mcpServers": {"filesystem": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/"], "transportType": "stdio"}, "browser_search": {"command": "python", "args": ["external-mcp-servers/browser_search/mcp_server.py"], "timeout": 180, "env": {"PYTHONUNBUFFERED": "1", "DISPLAY": ":0", "PYTHONPATH": "external-mcp-servers/browser_search/venv/lib/python3.11/site-packages/"}, "transportType": "stdio"}}}