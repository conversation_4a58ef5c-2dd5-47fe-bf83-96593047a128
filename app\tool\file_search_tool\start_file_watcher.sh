#!/bin/bash
# File Watcher Service Startup Script for Linux
# This shell script starts the file watcher service

echo "Starting File Watcher Service..."
echo ""

# Check if Python is available
if ! command -v python &> /dev/null
then
    echo "Error: Python is not installed or not in PATH"
    echo "Please install Python and try again"
    read -p "Press any key to continue..."
    exit 1
fi

# Install dependencies if needed
echo "Checking dependencies..."
pip install watchdog &> /dev/null

# Start the file watcher service
echo "Starting file watcher..."
python start_file_watcher.py

echo ""
echo "File watcher service has stopped."
read -p "Press any key to continue..."
