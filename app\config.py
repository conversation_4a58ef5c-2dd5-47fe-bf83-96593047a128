import json
from pathlib import Path
import threading
try:
    import tomllib
except ImportError:
    import tomli as tomllib
from typing import Dict, List, Optional


from pydantic import BaseModel, Field
import os

def get_project_root() -> Path:
    """Get the project root directory"""
    return Path(__file__).resolve().parent.parent

PROJECT_ROOT = get_project_root()
WORKSPACE_ROOT = PROJECT_ROOT / "workspace"

class LLMSettings(BaseModel):
    model: str = Field(..., description="Model name")
    base_url: str = Field(..., description="API base URL")
    api_key: str = Field(..., description="API key")
    max_tokens: int = Field(4096, description="Maximum number of tokens per request")
    max_input_tokens: Optional[int] = Field(
        None, description="Maximum input tokens to use across all requests (None for unlimited)"
    )
    temperature: float = Field(1.0, description="Sampling temperature")
    api_type: str = Field(..., description="Azure, Openai, or Ollama")
    api_version: str = Field(..., description="Azure Openai version if AzureOpenai")

class ProxySettings(BaseModel):
    server: Optional[str] = Field(None, description="Proxy server address")
    username: Optional[str] = Field(None, description="Proxy username")
    password: Optional[str] = Field(None, description="Proxy password")

class SearchSettings(BaseModel):
    engine : str = Field(default="default", description="")
    fallback_engines : List[str] = Field(
        default_factory=list, description="Extra arguments to pass to the browser"
    )
    retry_delay: int = Field(
        default=60,
        description="Seconds to wait before retrying all engines again after they all fail",
    )
    max_retries: int = Field(
        default=3,
        description="Maximum number of times to retry all engines when all fail",
    )
    lang: str = Field(
        default="en",
        description="Language code for search results (e.g., en, zh, fr)",
    )
    country: str = Field(
        default="us",
        description="Country code for search results (e.g., us, cn, uk)",
    )

class BrowserSettings(BaseModel):
    headless: bool = Field(False, description="Whether to run browser in headless mode")
    disable_security: bool = Field(
        True, description="Disable browser security features"
    )
    extra_chromium_args: List[str] = Field(
        default_factory=list, description="Extra arguments to pass to the browser"
    )
    chrome_instance_path: Optional[str] = Field(
        None, description="Path to a Chrome instance to use"
    )
    wss_url: Optional[str] = Field(
        None, description="Connect to a browser instance via WebSocket"
    )
    cdp_url: Optional[str] = Field(
        None, description="Connect to a browser instance via CDP"
    )
    proxy: Optional[ProxySettings] = Field(
        None, description="Proxy settings for the browser"
    )
    max_content_length: int = Field(
        2000, description="Maximum length for content retrieval operations"
    )

class SandboxSettings(BaseModel):
    """Configuration for the execution sandbox"""

    use_sandbox: bool = Field(False, description="Whether to use the sandbox")
    image: str = Field("python:3.12-slim", description="Base image")
    work_dir: str = Field("/workspace", description="Container working directory")
    memory_limit: str = Field("512m", description="Memory limit")
    cpu_limit: float = Field(1.0, description="CPU limit")
    timeout: int = Field(300, description="Default command timeout (seconds)")
    network_enabled: bool = Field(
        False, description="Whether network access is allowed"
    )


class MCPServerConfig(BaseModel):
    """Configuration for a single MCP server"""
    
    transportType: str = Field(..., description="Server connection type (sse or stdio)")
    url: Optional[str] = Field(None, description="Server URL for SSE connections")
    command: Optional[str] = Field(None, description="Command for stdio connections")
    args: List[str] = Field(
        default_factory=list, description="Arguments for stdio command"
    )
    autoApprove: List[str] = Field(
        default_factory=list, description="List of auto-approved operations"
    )
    disabled: bool = Field(
        False, description="Whether this server is disabled"
    )
    timeout: int = Field(
        60, description="Server operation timeout in seconds"
    )

class MCPSettings(BaseModel):
    """Configuration for MCP (Model Context Protocol)"""

    server_reference: str = Field(
        "app.mcp.server", description="Module reference for the MCP server"
    )
    servers: Dict[str, MCPServerConfig] = Field(
        default_factory=dict, description="MCP server configurations"
    )

    @classmethod
    def load_server_config(cls) -> Dict[str, MCPServerConfig]:
        """Load MCP server configuration from JSON file"""
        home_dir = os.environ.get("HOME", os.path.expanduser("~"))
        config_path = Path(os.path.join(home_dir, ".config","ruyi-ai", "mcp.json"))

        try:
            config_file = config_path if config_path.exists() else None
            if not config_file:
                return {}

            with config_file.open() as f:
                data = json.load(f)
                servers = {}

                for server_id, server_config in data.get("mcpServers", {}).items():
                    servers[server_id] = MCPServerConfig(
                        transportType=server_config["transportType"],
                        url=server_config.get("url"),
                        command=server_config.get("command"),
                        args=server_config.get("args", []),
                        autoApprove=server_config.get("autoApprove", []),
                        disabled=server_config.get("disabled", False),
                        timeout=server_config.get("timeout", 60)
                    )
                return servers
        except Exception as e:
            raise ValueError(f"Failed to load MCP server config: {e}")

class FileWatcherSettings(BaseModel):
    """Configuration for file watcher"""

    enabled: bool = Field(True, description="Whether file watcher is enabled")
    watch_paths: List[str] = Field(default_factory=list, description="Paths to monitor")
    ignore_patterns: List[str] = Field(default_factory=list, description="Patterns to ignore")
    batch_size: int = Field(100, description="Batch size for processing events")
    batch_timeout: float = Field(5.0, description="Timeout for batch processing")
    update_interval: float = Field(1.0, description="Database update interval")
    max_file_size: int = Field(10485760, description="Maximum file size to index")

class ImageGenerationSettings(BaseModel):
    """Configuration for ModelScope API"""
    api_key: str = Field("", description="ModelScope API key")
    base_url: str = Field(
        "https://api-inference.modelscope.cn/v1/images/generations",
        description="ModelScope API base URL"
    )
    default_model: str = Field(
        "MusePublic/47_ckpt_SD_XL",
        description="Default model for image generation"
    )

class VideoGenerationSettings(BaseModel):
    """Configuration for video generation"""
    default_model: str = Field("Wan-AI/Wan2.1-T2V-14B", description="default model")
    base_url: str = Field("https://api.siliconflow.cn", description="default base URL")
    api_key: str = Field(..., description="api key for video generation service")

class AppConfig(BaseModel):
    '''顶层配置，整合所有子配置'''
    llm: Dict[str, LLMSettings]
    sandbox: Optional[SandboxSettings] = Field(None, description="Sandbox configuration")
    browser_config: Optional[BrowserSettings] = Field(None, description="Browser configuration")
    search_config: Optional[SearchSettings] = Field(
        None, description="Search configuration"
    )
    mcp_config: Optional[MCPSettings] = Field(None, description="MCP configuration")
    file_watcher_config: Optional[FileWatcherSettings] = Field(None, description="File watcher configuration")
    image_generation: Optional[ImageGenerationSettings] = Field(None, description="image generation configuration")
    video_generation: Optional[VideoGenerationSettings] = Field(None, description="video generation configuration")

class Config:
    _instance = None
    _lock = threading.Lock()
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self._config = None
                    self._load_initial_config()
                    self._initialized = True
    
    def _get_config_path(self) -> Path:
        root = PROJECT_ROOT
        config_path = root / 'config' / 'config.toml'
        if config_path.exists():
            return config_path
        example_path = root / "config" / "config.example.toml"
        if example_path.exists():
            return example_path
        raise FileNotFoundError("No configuration file found in config directory")
        
    def _load_config(self) -> dict:
        config_path = self._get_config_path()
        with config_path.open("rb") as f:
            return tomllib.load(f)
    
    def _load_initial_config(self):
        raw_config = self._load_config()
        llm_base = raw_config.get("llm", {})
        llm_overrides = {k:v for k,v in raw_config.get("llm", {}).items() if isinstance(v, dict)}
        default_llm_settings = {
            "model": llm_base.get("model"),
            "base_url": llm_base.get("base_url"),
            "api_key": llm_base.get("api_key"),
            "max_tokens": llm_base.get("max_tokens", 4096),
            "max_input_tokens": llm_base.get("max_input_tokens"),
            "temperature": llm_base.get("temperature", 1.0),
            "api_type": llm_base.get("api_type", ""),
            "api_version": llm_base.get("api_version", ""),
        }

        # handle browser config.
        browser_config = raw_config.get("browser", {})
        browser_settings = None

        if browser_config:
             # handle proxy settings.
            proxy_config = browser_config.get("proxy", {})
            proxy_settings = None
            if proxy_config and proxy_config.get("server"):
                proxy_settings = ProxySettings(
                    **{
                        k:v
                        for k,v in proxy_config.items()
                        if k in ["server", "username", "password"] and v
                    }
                )
        
            # filter valid browser config parameters.
            valid_browser_params = {
                k: v
                for k,v in browser_config.items()
                if k in BrowserSettings.__annotations__  and v is not None
            }

            # if there is proxy settings, add it to the parameters.
            if proxy_settings:
                valid_browser_params["proxy"] = proxy_settings

            # only create BrowserSettings when there are valid parameters.
            if valid_browser_params:
                browser_settings = BrowserSettings(**valid_browser_params)

        search_config = raw_config.get("search", {})
        search_settings = None
        if search_config:
            search_settings = SearchSettings(**search_config)

        sandbox_config = raw_config.get("sandbox", {})
        sandbox_settings = SandboxSettings(
            use_sandbox=sandbox_config.get("use_sandbox", False),
            image=sandbox_config.get("image", "python:3.12-slim"),
            work_dir=sandbox_config.get("work_dir", "/workspace"),
            memory_limit=sandbox_config.get("memory_limit", "512m"),
            cpu_limit=sandbox_config.get("cpu_limit", 1.0),
            timeout=sandbox_config.get("timeout", 300),
            network_enabled=sandbox_config.get("network_enabled", False)
        ) if sandbox_config else SandboxSettings(
            use_sandbox=False,
            image="python:3.12-slim",
            work_dir="/workspace",
            memory_limit="512m",
            cpu_limit=1.0,
            timeout=300,
            network_enabled=False
        )
        
        mcp_config = raw_config.get("mcp", {})
        mcp_settings = None
        if mcp_config:
            # Load server configurations from JSON
            servers = MCPSettings.load_server_config()
            mcp_settings = MCPSettings(
                server_reference=mcp_config.get("server_reference", "app.mcp.server"),
                servers=servers
            )
        else:
            mcp_settings = MCPSettings(
                server_reference="app.mcp.server",
                servers=MCPSettings.load_server_config()
            )

        file_watcher_config = raw_config.get("file_watcher", {})
        file_watcher_settings = None
        if file_watcher_config:
            file_watcher_settings = FileWatcherSettings(**file_watcher_config)

        imagegeneration_config = raw_config.get("image_generation", {})
        imagegeneration_settings = None
        if imagegeneration_config:
            imagegeneration_settings = ImageGenerationSettings(**imagegeneration_config)

        videogeneration_config = raw_config.get("video_generation", {})
        videogeneration_settings = None
        if videogeneration_config:
            videogeneration_settings = VideoGenerationSettings(**videogeneration_config)

        config_dict = {
            "llm": {
                "default": default_llm_settings,
                **{
                    name: {**default_llm_settings, **override_config}
                    for name, override_config in llm_overrides.items()
                }
            },
            "sandbox": sandbox_settings,
            "browser_config": browser_settings,
            "search_config": search_settings,
            "mcp_config": mcp_settings,
            "file_watcher_config": file_watcher_settings,
            "image_generation": imagegeneration_settings,
            "video_generation": videogeneration_settings,
        }

        self._config = AppConfig(**config_dict)
    
    @property
    def llm(self) -> Dict[str, LLMSettings]:
        if not self._config:
            return {}
        return self._config.llm
    
    @property
    def sandbox(self) -> SandboxSettings:
        if not self._config or not self._config.sandbox:
            return SandboxSettings(
                use_sandbox=False,
                image="python:3.12-slim",
                work_dir="/workspace",
                memory_limit="512m",
                cpu_limit=1.0,
                timeout=300,
                network_enabled=False
            )
        return self._config.sandbox

    @property
    def browser_config(self) -> Optional[BrowserSettings]:
        if not self._config:
            return None
        return self._config.browser_config

    @property
    def search_config(self) -> Optional[SearchSettings]:
        if not self._config:
            return None
        return self._config.search_config

    @property
    def mcp_config(self) -> MCPSettings:
        """Get the MCP configuration"""
        if not self._config or not self._config.mcp_config:
            return MCPSettings(
                server_reference="app.mcp.server",
                servers={}
            )
        return self._config.mcp_config

    @property
    def file_watcher_config(self) -> Optional[FileWatcherSettings]:
        """Get the file watcher configuration"""
        if not self._config:
            return None
        return self._config.file_watcher_config

    @property
    def workspace_root(self) -> Path:
        """Get the workspace root directory"""
        return WORKSPACE_ROOT

    @property
    def root_path(self) -> Path:
        """Get the root path of the application"""
        return PROJECT_ROOT

    @property
    def image_generation(self) -> Optional[ImageGenerationSettings]:
        """Get the ModelScope configuration"""
        if not self._config:
            return None
        return self._config.image_generation

    @property
    def video_generation(self) -> Optional[VideoGenerationSettings]:
        """Get the video generation configuration"""
        if not self._config:
            return None
        return self._config.video_generation

config = Config()

if __name__ == "__main__":
    from app.logger import logger  # 延迟导入
    logger.info(config)
