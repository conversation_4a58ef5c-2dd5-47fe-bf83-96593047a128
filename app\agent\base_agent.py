from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from typing import AsyncGenerator, List, Optional, Union, Any, Dict
from pydantic import BaseModel, Field, model_validator

from app.utils.event_manager import EventManager

from app.logger import logger
from app.llm.llm import LLM
from app.memory.in_memory_storage import InMemorySessionStorage
from app.memory.memory import Memory
from app.memory.session_storage import SessionStorage
from app.schema import AgentState, Message


class BaseAgent(BaseModel, ABC):
    """
    定义 BaseAgent 抽象基类，管理智能代理的状态、记忆和执行循环，为具体代理（如 MCPAgent）提供基础。
    依赖: LLM（语言模型）、Memory（记忆存储）、AgentState（状态枚举）、SANDBOX_CLIENT（沙盒客户端）。

    BaseAgent 提供通用功能：
        状态管理（state_context）&任务循环（run）。
        记忆存储（memory）。
        语言模型集成（llm）。
    """
    name: str = Field(..., description="代理唯一标识")
    description: str = Field(..., description="代理唯一标识")
    llm: LLM = Field(default_factory=LLM, description="语言模型实例")
    # memory: Memory = Field(default_factory=Memory, description="存储消息历史")
    session_storage: SessionStorage = Field(default_factory=InMemorySessionStorage, description="用户会话存储实例")
    state: AgentState = Field(default=AgentState.IDLE, description="当前状态（如 IDLE、RUNNING）")
    event_manager: Optional[EventManager] = None # 新增属性

    # 提示词
    system_prompt: Optional[str] = Field(
        None, description="System-level instruction prompt"
    )
    next_step_prompt: Optional[str] = Field(
        None, description="Prompt for determining next action"
    )

    # 执行控制
    max_steps: int = Field(default=3, description="最大执行步数，默认 10")
    current_step: int = Field(default=0, description="当前步数")

    duplicate_threshold: int = Field(default=2, description="检测重复响应的阈值（2 次）")

    class Config:
        arbitrary_types_allowed = True      # 允许任意类型（如 LLM、Memory）。
        extra = "allow"                     # 接受子类额外字段，增强扩展性。

    # Pydantic 提供的装饰器，用于在模型实例化后执行自定义验证或初始化逻辑。
    # * 当创建 BaseAgent 实例时（如 agent = BaseAgent(name="test")），Pydantic 先解析字段（如 name、llm），然后调用 initialize_agent。
    # * 在实例化后但返回实例前执行。
    @model_validator(mode="after")
    def initialize_agent(self) -> "BaseAgent":
        if self.llm is None or not isinstance(self.llm, LLM):
            self.llm = LLM(config_name=self.name.lower())
        # if not isinstance(self.memory, Memory):
        #     self.memory = Memory()
        if not isinstance(self.session_storage, SessionStorage):
            self.session_storage = InMemorySessionStorage()
        return self
    
    @asynccontextmanager
    async def state_context(self, new_state: AgentState):
        """异步上下文管理器，安全切换代理状态。（如 IDLE -> RUNNING -> IDLE）

        Args:
            new_state: The state to transition to during the context.

        Yields:
            None: Allows execution within the new state.

        Raises:
            ValueError: If the new_state is invalid.
        """
        if not isinstance(new_state, AgentState):
            raise ValueError(f"Invalid state: {new_state}")

        # 保存当前状态，切换到新状态。
        previous_state = self.state
        self.state = new_state
        try:
            # 执行上下文代码（yield）。
            yield
        except Exception as e:
            # 异常时转为 ERROR 状态
            self.state = AgentState.ERROR 
            raise e
        finally:
            # 无论成功或失败，最终恢复原状态。
            self.state = previous_state

    async def run(self, user_id: str, 
                  request: Optional[str] = None, 
                  image_urls: Optional[List[str]] = None, 
                  with_history: bool=True, 
                  event_manager: Optional[EventManager] = None,
                  session_id: Optional[str] = None
                  ) -> Union[str, AsyncGenerator[str, None]]:
        """处理用户请求,驱动代理任务执行，控制步数和状态。
        args:
            user_id: 用户唯一标识
            request：可选的要处理的初始用户请求。
        return:
            总结执行结果的字符串。
        raise:
            RuntimeError：如果代理在启动时未处于IDLE状态。
        """
        # 1.获取用户会话
        self.event_manager = event_manager # 接收并保存 event_manager
        memory = self.session_storage.get_session(user_id=user_id)
        self.memory = memory  # 将当前用户的 memory 绑定到代理

        # 检查初始状态为 IDLE。
        # 2.保存用户请求到记忆。
        if request:
            if image_urls:
                # 如果有图像 URL，保存为用户消息。
                self.memory.update_memory("user", request, image_urls=image_urls)
            else:
                # 仅文本请求。
                self.memory.update_memory("user", request)
        # 在 RUNNING 状态下：
        results: List[str] = []
        async with self.state_context(AgentState.RUNNING):
            # 3.循环执行 step 方法（子类实现）。
            while(self.current_step < self.max_steps and self.state != AgentState.FINISHED):
                self.current_step += 1
                logger.info(f"Executing step {self.current_step}/{self.max_steps}")
                # 示例：在 Agent 思考过程中发布事件
                if self.event_manager:
                    await self.event_manager.publish(user_id, "thought", {"agent_name": self.name, "thought_process": "Agent 正在思考..."}, session_id)
                
                step_result = await self.step(with_history)
                
                if self.state == AgentState.WAITING_FOR_HUMAN_INPUT:
                    logger.info(f"Agent '{self.name}' is waiting for human input.")
                    return step_result

                # 检测是否卡住（is_stuck），调整策略。
                # 收集每步结果。
                results.append(f"Step {self.current_step}: {step_result}")
            # 达到 max_steps 或 FINISHED 状态时退出。
            if self.current_step >= self.max_steps:  # todo状态重置
                self.current_step = 0
                self.state = AgentState.IDLE
                results.append(f"Terminated: Reached max steps ({self.max_steps})")
        # 清理沙盒资源，返回结果字符串。
        return "\n".join(results) if results else "No steps executed"
    
    # @abstractmethod
    async def step(self, with_history: bool) -> Any: # 将返回类型改为 Any
        """定义单步执行逻辑，子类必须实现。"""

    # @property
    # def messages(self) -> List[Message]:
    #     return self.memory.messages
    
    # @messages.setter
    # def messages(self, messages: List[Message]) -> None:
    #     self.memory.messages = messages
