# 后端到前端实时通信机制设计方案

## 1. 目标

在 Agent 流程执行期间（例如：Agent 选择、工具调用开始/结束、分析结果等）将状态更新实时发送到前端，提升用户体验。

## 2. 现有框架分析

*   **FastAPI 框架：** 确认存在，且 `app/api/workflow_routes.py` 已支持 `StreamingResponse`，为 Server-Sent Events (SSE) 提供了基础。
*   **Agent 流程：**
    *   `app/router/router.py` 负责 Agent 的路由分发和选择。
    *   `app/analyst/analyst.py` 负责对 Agent 执行结果进行分析和总结。
    *   Agent 的执行（如 `BaseAgent.run()`）和工具调用（如 `BaseTool.execute()`）是产生状态更新的关键点。

## 3. 技术选型：Server-Sent Events (SSE)

**推荐理由：**

*   **与现有 FastAPI 兼容：** FastAPI 的 `StreamingResponse` 可以直接用于实现 SSE。
*   **轻量级：** 相较于 WebSocket，SSE 协议更简单，开销更小，适用于单向（服务器到客户端）的实时数据推送场景。
*   **自动重连：** 浏览器内置的 `EventSource` API 支持自动重连，简化了前端的错误处理。
*   **HTTP/2 兼容：** SSE 可以很好地利用 HTTP/2 的多路复用特性。

## 4. 数据模型：AgentStatusUpdate

为了标准化所有状态更新消息的格式，定义一个 Pydantic 模型：

```python
from pydantic import BaseModel
from typing import Optional, Dict, Any

class AgentStatusUpdate(BaseModel):
    """
    Agent 流程状态更新的数据模型。
    """
    event_type: str  # 事件类型，例如 "agent_selection", "tool_call_start", "thought", "final_result"
    timestamp: str   # 时间戳，ISO 格式
    user_id: str     # 用户 ID
    session_id: Optional[str] = None # 会话 ID
    data: Dict[str, Any] # 具体的事件数据，根据 event_type 包含不同内容
```

`data` 字段的示例内容：

*   **`agent_routing_start`**: `{"request": "用户请求内容"}`
*   **`agent_selection`**: `{"agent_name": "选定的 Agent 名称", "task_type": "任务类型"}`
*   **`thought`**: `{"agent_name": "Agent 名称", "thought_process": "Agent 的思考过程"}`
*   **`tool_call_start`**: `{"agent_name": "Agent 名称", "tool_name": "工具名称", "tool_input": "工具输入"}`
*   **`tool_call_end`**: `{"agent_name": "Agent 名称", "tool_name": "工具名称", "tool_output": "工具输出"}`
*   **`analyst_summary`**: `{"summary_result": "Analyst 总结结果", "action": "Analyst 决定采取的行动"}`
*   **`final_result`**: `{"message": "最终结果消息", "media_url": "媒体 URL (可选)"}`

## 5. SSE 广播器：EventManager

创建一个 `EventManager` 类来管理 SSE 连接和消息广播。

```python
# app/utils/event_manager.py (示例路径)
import asyncio
import json
from datetime import datetime
from typing import Dict, AsyncGenerator, Any

class EventManager:
    def __init__(self):
        self.connections: Dict[str, asyncio.Queue] = {} # {user_id: Queue}

    async def connect(self, user_id: str) -> AsyncGenerator[str, None]:
        """
        为指定用户建立 SSE 连接，并返回一个异步生成器。
        """
        queue = asyncio.Queue()
        self.connections[user_id] = queue
        try:
            while True:
                message = await queue.get()
                yield f"data: {json.dumps(message)}\n\n"
        except asyncio.CancelledError:
            # 连接关闭时清理
            del self.connections[user_id]
            print(f"SSE connection for {user_id} closed.")

    async def publish(self, user_id: str, event_type: str, data: Dict[str, Any], session_id: Optional[str] = None):
        """
        向指定用户发布状态更新事件。
        """
        if user_id in self.connections:
            update = AgentStatusUpdate(
                event_type=event_type,
                timestamp=datetime.now().isoformat(),
                user_id=user_id,
                session_id=session_id,
                data=data
            )
            await self.connections[user_id].put(update.dict())
            print(f"Published event '{event_type}' for user {user_id}")
```

## 6. FastAPI 路由集成

### 6.1. 新增 SSE 路由

在 `app/api/workflow_routes.py` 中添加一个新的 SSE 路由：

```python
# app/api/workflow_routes.py
from fastapi import APIRouter, Request, Depends
from fastapi.responses import StreamingResponse
from app.utils.event_manager import EventManager # 假设 EventManager 放在 app/utils

# ... (其他导入)

workflow_router = APIRouter(prefix="/v2", tags=["对话"])

# 在 FastAPI 应用启动时初始化 EventManager
# app = FastAPI()
# app.state.event_manager = EventManager()

@workflow_router.get("/stream/{user_id}", summary="获取Agent流程实时状态更新", response_class=StreamingResponse)
async def stream_agent_status(user_id: str, request: Request):
    """
    前端通过此接口建立 SSE 连接，接收 Agent 流程的实时状态更新。
    """
    event_manager: EventManager = request.app.state.event_manager
    return StreamingResponse(event_manager.connect(user_id), media_type="text/event-stream")
```

### 6.2. 在 `/v2/chat` 路由中发布事件

修改 `app/api/workflow_routes.py` 中的 `chat_completions` 函数，在关键节点发布事件。

```python
# app/api/workflow_routes.py
# ... (导入 EventManager)

@workflow_router.post("/chat", summary="与llm模型对话", response_model=None)
async def chat_completions(request: Request, body: ChatAPIInput) -> Union[ChatAPIOutput, StreamingResponse]:
    event_manager: EventManager = request.app.state.event_manager
    user_id = body.userId
    session_id = body.sessionId # 假设 sessionId 存在于 ChatAPIInput

    # 1. 检验请求合法性 (此处省略)

    # 2. 路由分发：单智能体/多智能体模式
    # 获取预先创建的agents
    expert_agents: Dict[str, BaseAgent] = request.app.state.expert_agents
    agents: Dict[str, BaseAgent] = {
        "commander": CommanderAgent(agents=expert_agents),
        **expert_agents
    }
    text_content = ""
    image_urls = []
    if not isinstance(body.content, list) or len(body.content) <= 0:
        raise ValueError(f"invalid content:{body.content}")
    for msg in body.content:
        if msg["type"] == "text":
            text_content +=  msg.get("text", "")
        elif msg["type"] == "image_url":
            image_urls.append(msg)

    # 发布路由开始事件
    await event_manager.publish(user_id, "agent_routing_start", {"request": text_content}, session_id)

    router = Router(agents=agents)
    task_type, agent_name = await router.route(request=text_content)
    agent = agents[agent_name]

    # 发布 Agent 选择事件
    await event_manager.publish(user_id, "agent_selection", {"agent_name": agent_name, "task_type": task_type}, session_id)

    # 3. 调用代理执行
    try:
        # ... (Agent 执行逻辑)
        # 在 Agent.run() 内部或其调用处，需要传递 event_manager 实例
        # 例如：agent_result = await agent.run(user_id=body.userId, request=text_content, image_urls=image_urls, with_history=True, event_manager=event_manager)
        # 具体如何传递和使用，将在 Agent/Tool 集成部分说明

        if agent_name == "qa":
            agent_result = agent.run(user_id=body.userId, request=text_content, image_urls=image_urls, with_history=True)
        elif agent_name == "video_agent":
            if hasattr(agent, "run") and "image_url" in agent.run.__code__.co_varnames:
                agent_result = await agent.run(user_id=body.userId, request=text_content, image_urls=image_urls, with_history=True)
            else:
                agent_result = await agent.run(user_id=body.userId, request=text_content, with_history=True)
        else:
            agent_result = await agent.run(user_id=body.userId, request=text_content, with_history=True)

    except Exception as e:
        logger.error(f"Agent execute failed: {str(e)}")
        # 发布错误事件
        await event_manager.publish(user_id, "agent_error", {"error_message": str(e)}, session_id)
        raise ValueError(f"Agent execute failed: {str(e)}")

    # 4. 处理流式或非流式响应
    if isinstance(agent_result, AsyncGenerator):
        async def stream_response_generator():
            async for chunk in agent_result:
                # 对于流式响应，如果 Agent 内部已经发布了思考过程等事件，这里只需转发最终文本块
                yield chunk
        return StreamingResponse(stream_response_generator(), media_type="text/event-stream")
    else:
        media_url = ""
        if task_type == "simple":
            analyst = Analyst()
            # 传递 event_manager 给 Analyst
            result = await analyst.summary(user_request=text_content,
                                        agent_result=agent_result,
                                        task_type=task_type,
                                        agent_name=agent_name,
                                        event_manager=event_manager, # 新增参数
                                        user_id=user_id, # 新增参数
                                        session_id=session_id # 新增参数
                                        )
            if agent_name == "image_agent" or agent_name == "video_agent":
                if isinstance(agent_result, str):
                    json_result = json.loads(agent_result)
                    result = json_result["message"]
                    media_url = json_result.get("media_url", "")
                else:
                    logger.info(f"Agent {agent_name} returned result: {agent_result}")
        else:
            result = agent_result
        
        # 发布最终结果事件
        await event_manager.publish(user_id, "final_result", {"message": result, "media_url": media_url}, session_id)

        logger.info(f"Returning ChatAPIOutput with media_url: {media_url!r}")
        return ChatAPIOutput(sn=body.sn,
                             userId=body.userId,
                             sessionId=body.sessionId,
                             name=None,
                             message=result,
                             media_url=media_url,
                             code=200)
```

## 7. Agent/Tool 集成

为了让 Agent 和 Tool 能够发布事件，需要将 `event_manager` 实例传递给它们。

### 7.1. 修改 `BaseAgent`

在 `BaseAgent` 的 `__init__` 方法中添加 `event_manager` 参数，并在 `run` 方法中调用 `event_manager.publish`。

```python
# app/agent/base_agent.py
from app.utils.event_manager import EventManager # 导入 EventManager

class BaseAgent(BaseModel):
    # ... (现有属性)
    event_manager: Optional[EventManager] = None # 新增属性

    class Config:
        arbitrary_types_allowed = True

    async def run(self, user_id: str, request: str, image_urls: Optional[List[Dict]] = None, with_history: bool = True, event_manager: Optional[EventManager] = None, session_id: Optional[str] = None) -> Union[str, AsyncGenerator]:
        """
        Agent 的核心运行方法。
        """
        self.event_manager = event_manager # 接收并保存 event_manager
        # ... (现有逻辑)

        # 示例：在 Agent 思考过程中发布事件
        if self.event_manager:
            await self.event_manager.publish(user_id, "thought", {"agent_name": self.name, "thought_process": "Agent 正在思考..."}, session_id)

        # 示例：在调用工具前发布事件
        # if self.event_manager:
        #     await self.event_manager.publish(user_id, "tool_call_start", {"agent_name": self.name, "tool_name": "some_tool", "tool_input": "..."})

        # ... (其他逻辑)
```

### 7.2. 修改 `BaseTool`

类似地，在 `BaseTool` 的 `__init__` 方法中添加 `event_manager` 参数，并在 `execute` 方法中调用 `event_manager.publish`。

```python
# app/tool/base_tool.py
from app.utils.event_manager import EventManager # 导入 EventManager

class BaseTool(BaseModel):
    # ... (现有属性)
    event_manager: Optional[EventManager] = None # 新增属性

    class Config:
        arbitrary_types_allowed = True

    async def execute(self, user_id: str, session_id: Optional[str] = None, **kwargs) -> str:
        """
        工具的核心执行方法。
        """
        # 示例：在工具执行开始时发布事件
        if self.event_manager:
            await self.event_manager.publish(user_id, "tool_call_start", {"tool_name": self.name, "tool_input": kwargs}, session_id)

        # ... (工具执行逻辑)
        tool_output = "工具执行结果" # 假设这是工具的输出

        # 示例：在工具执行结束时发布事件
        if self.event_manager:
            await self.event_manager.publish(user_id, "tool_call_end", {"tool_name": self.name, "tool_output": tool_output}, session_id)

        return tool_output
```

### 7.3. 修改 `Analyst`

在 `Analyst` 的 `summary` 方法中添加 `event_manager` 参数，并在分析完成后发布事件。

```python
# app/analyst/analyst.py
from app.utils.event_manager import EventManager # 导入 EventManager

class Analyst(BaseModel):
    llm: LLM = Field(default_factory=LLM, description="语言模型实例")

    class Config:
        arbitrary_types_allowed = True
        extra = "allow"

    async def summary(self, user_request: str, agent_result: str, task_type: str, agent_name: str, event_manager: Optional[EventManager] = None, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        response = ""
        try:
            # ... (现有逻辑)
            summary_result = json.loads(clean_json)
            is_goal_achieved = summary_result.get("is_goal_achieved")
            action = summary_result.get("action")
            result = summary_result.get("result")
            reason = summary_result.get("reason")

            # 发布 Analyst 总结事件
            if event_manager and user_id:
                await event_manager.publish(user_id, "analyst_summary", {
                    "is_goal_achieved": is_goal_achieved,
                    "action": action,
                    "result": result,
                    "reason": reason
                }, session_id)

            # ... (其他逻辑)
            return result
        except Exception as e:
            # ... (错误处理)
            return agent_result
```

### 7.4. 在 `main.py` 中初始化 `EventManager`

确保在 FastAPI 应用启动时初始化 `EventManager` 并将其存储在 `app.state` 中。

```python
# main.py
from fastapi import FastAPI
from app.api.workflow_routes import workflow_router
from app.utils.event_manager import EventManager # 导入 EventManager

app = FastAPI()

# 初始化 EventManager
app.state.event_manager = EventManager()

app.include_router(workflow_router)

# ... (其他配置)
```

## 8. 前端集成

前端将使用 `EventSource` API 连接到 SSE 路由，并监听不同类型的事件以实时更新 UI。

```javascript
// 示例前端 JavaScript 代码
const userId = "your_user_id"; // 从用户会话中获取
const eventSource = new EventSource(`/v2/stream/${userId}`);

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log("Received SSE event:", data);

    // 根据 event_type 更新 UI
    switch (data.event_type) {
        case "agent_routing_start":
            // 显示 "Agent 正在分析您的请求..."
            break;
        case "agent_selection":
            // 显示 "Agent 已选择: " + data.data.agent_name
            break;
        case "thought":
            // 显示 Agent 的思考过程: data.data.thought_process
            break;
        case "tool_call_start":
            // 显示 "Agent 正在调用工具: " + data.data.tool_name
            break;
        case "tool_call_end":
            // 显示 "工具 " + data.data.tool_name + " 执行完成，输出: " + data.data.tool_output
            break;
        case "analyst_summary":
            // 显示 Analyst 的总结和决定
            break;
        case "final_result":
            // 显示最终结果: data.data.message
            break;
        case "agent_error":
            // 显示错误信息: data.data.error_message
            break;
        default:
            console.warn("Unknown event type:", data.event_type);
    }
};

eventSource.onerror = function(error) {
    console.error("EventSource failed:", error);
    eventSource.close(); // 关闭连接，可能需要重试
};

// 同时，前端会发送对话请求到 /v2/chat
// fetch('/v2/chat', {
//     method: 'POST',
//     headers: { 'Content-Type': 'application/json' },
//     body: JSON.stringify({ userId: userId, content: [{type: "text", text: "你的问题"}] })
// })
// .then(response => response.json()) // 或 response.text() 如果是流式文本
// .then(data => {
//     console.log("Final chat response:", data);
//     // 处理最终的对话结果
// })
// .catch(error => console.error('Error:', error));
```

## 9. Mermaid 图

```mermaid
graph TD
    A[前端] -->|HTTP POST /v2/chat| B(FastAPI workflow_routes.py)
    A -->|HTTP GET /v2/stream/{user_id}| C(SSE EventManager)

    B -->|发布 agent_routing_start 事件| C
    B -->|调用 Router.route()| D(Router.py)
    D -->|返回 task_type, agent_name| B
    B -->|发布 agent_selection 事件| C

    B -->|调用 Agent.run()| E(BaseAgent/SpecificAgent)
    E -->|Agent 内部思考/执行| F(LLM)
    E -->|发布 thought 事件| C
    E -->|调用 Tool.execute()| G(BaseTool/SpecificTool)
    G -->|发布 tool_call_start 事件| C
    G -->|执行工具逻辑| H(外部服务/文件系统等)
    H -->|返回工具输出| G
    G -->|发布 tool_call_end 事件| C
    G -->|返回工具结果| E
    E -->|返回 Agent 结果| B

    B -->|调用 Analyst.summary() (如果 task_type == "simple")| I(Analyst.py)
    I -->|返回分析结果| B
    B -->|发布 analyst_summary 事件| C

    B -->|发布 final_result 事件| C
    C -->|SSE 流式传输状态更新| A