
import math
from typing import AsyncGenerator, Dict, List, Optional, Union

from openai.types.chat import Cha<PERSON><PERSON><PERSON>ple<PERSON>, ChatCompletionMessage
from openai import APIError, AsyncAzureOpenAI, AsyncOpenAI, AuthenticationError, OpenAIError, RateLimitError
import tiktoken

from app.logger import logger
from app.config import LLMSettings, config
from app.exception import ModelNotSupported, TokenLimitExceeded
from app.schema import ROLE_VALUES, TOOL_CHOICE_TYPE, TOOL_CHOICE_VALUES, Message, ToolChoice

REASONING_MODELS = ["qwen-max"]
MULTIMODAL_MODELS = ["qwen-vl-max"]

"""
qwen api：https://help.aliyun.com/zh/model-studio/use-qwen-by-calling-api
qwen models：https://help.aliyun.com/zh/model-studio/models
"""

class TokenCounter:
    '''计算消息、图像和工具调用的 token 数'''
    # Token constants
    BASE_MESSAGE_TOKENS = 4
    FORMAT_TOKENS = 2
    LOW_DETAIL_IMAGE_TOKENS = 85
    HIGH_DETAIL_TILE_TOKENS = 170

    # Image processing constants
    MAX_SIZE = 2048
    HIGH_DETAIL_TARGET_SHORT_SIDE = 768
    TILE_SIZE = 512

    def __init__(self, tokenizer):
        self.tokenizer = tokenizer
    
    def count_text(self, text: str) -> int:
        '''计算文本 token 数，空文本返回 0'''
        return 0 if not text else len(self.tokenizer.encode(text))

    def count_image(self, image_item: dict) -> int:
        '''
        Calculate tokens for an image based on detail level and dimensions

        For "low" detail: fixed 85 tokens
        For "high" detail:
        1. Scale to fit in 2048x2048 square
        2. Scale shortest side to 768px
        3. Count 512px tiles (170 tokens each)
        4. Add 85 tokens
        '''
        detail = image_item.get("detail", "medium")

        # For low detail, always return fixed token count
        if detail == "low":
            return self.LOW_DETAIL_IMAGE_TOKENS

        # For medium detail (default in OpenAI), use high detail calculation
        # OpenAI doesn't specify a separate calculation for medium

        # For high detail, calculate based on dimensions if available
        if detail == "high" or detail == "medium":
            # If dimensions are provided in the image_item
            if "dimensions" in image_item:
                width, height = image_item["dimensions"]
                return self._calculate_high_detail_tokens(width, height)

        # Default values when dimensions aren't available or detail level is unknown
        if detail == "high":
            # Default to a 1024x1024 image calculation for high detail
            return self._calculate_high_detail_tokens(1024, 1024)  # 765 tokens
        elif detail == "medium":
            # Default to a medium-sized image for medium detail
            return 1024  # This matches the original default
        else:
            # For unknown detail levels, use medium as default
            return 1024

    def _calculate_high_detail_tokens(self, width: int, height: int) -> int:
        """Calculate tokens for high detail images based on dimensions"""
        # Step 1: Scale to fit in MAX_SIZE x MAX_SIZE square
        if width > self.MAX_SIZE or height > self.MAX_SIZE:
            scale = self.MAX_SIZE / max(width, height)
            width = int(width * scale)
            height = int(height * scale)

        # Step 2: Scale so shortest side is HIGH_DETAIL_TARGET_SHORT_SIDE
        scale = self.HIGH_DETAIL_TARGET_SHORT_SIDE / min(width, height)
        scaled_width = int(width * scale)
        scaled_height = int(height * scale)

        # Step 3: Count number of 512px tiles
        tiles_x = math.ceil(scaled_width / self.TILE_SIZE)
        tiles_y = math.ceil(scaled_height / self.TILE_SIZE)
        total_tiles = tiles_x * tiles_y

        # Step 4: Calculate final token count
        return (
            total_tiles * self.HIGH_DETAIL_TILE_TOKENS
        ) + self.LOW_DETAIL_IMAGE_TOKENS

    def count_content(self, content: Union[str, List[Union[str, dict]]]) -> int:
        """
        处理消息内容：
            字符串：直接计数。
            列表：遍历文本（count_text）或图像（count_image）。
        """
        if not content:
            return 0
        
        if isinstance(content, str):
            return self.count_text(content)
        
        token_count = 0
        for item in content:
            if isinstance(item, str):
                token_count += self.count_text(item)
            elif isinstance(item, dict):
                if "text" in item:
                    token_count += self.count_text(item["text"])
                elif "image_url" in item:
                    token_count += self.count_image(item)
        return token_count

    def count_tool_calls(self, tool_calls: List[dict]) -> int:
        """Calculate tokens for tool calls"""
        token_count = 0
        for tool_call in tool_calls:
            if "function" in tool_call:
                function = tool_call["function"]
                token_count += self.count_text(function.get("name", ""))
                token_count += self.count_text(function.get("arguments", ""))
        return token_count

    def count_message_tokens(self, messages: List[dict]) -> int:
        """Calculate the total number of tokens in a message list"""
        total_tokens = self.FORMAT_TOKENS  # Base format tokens

        for message in messages:
            tokens = self.BASE_MESSAGE_TOKENS  # Base tokens per message

            # Add role tokens
            tokens += self.count_text(message.get("role", ""))

            # Add content tokens
            if "content" in message:
                tokens += self.count_content(message["content"])

            # Add tool calls tokens
            if "tool_calls" in message:
                tokens += self.count_tool_calls(message["tool_calls"])

            # Add name and tool_call_id tokens
            tokens += self.count_text(message.get("name", ""))
            tokens += self.count_text(message.get("tool_call_id", ""))

            total_tokens += tokens

        return total_tokens

class LLM:
    '''与LLM交互，处理消息、图像和工具调用，并管理 token 计数。'''
    _instances: Dict[str, "LLM"] = {}

    def __new__(cls, config_name:str = "default"):
        if config_name not in cls._instances:
            instance = super().__new__(cls)
            instance.__init__(config_name)
            cls._instances[config_name] = instance
        return cls._instances[config_name]
    
    def __init__(self, config_name:str = "default"):
        # Only initialize if not already initialized
        if not hasattr(self, "client"): 
            llm_config = config.llm.get(config_name, config.llm["default"])
            self.model = llm_config.model
            self.max_tokens = llm_config.max_tokens
            self.temperature = llm_config.temperature
            self.api_type = llm_config.api_type
            self.api_key = llm_config.api_key
            self.api_version = llm_config.api_version
            self.base_url = llm_config.base_url

            # Add token counting related attributes
            self.total_input_tokens = 0
            self.total_completion_tokens = 0
            self.max_input_tokens = (
                llm_config.max_input_tokens 
                if hasattr(llm_config, "max_input_tokens") 
                else None
            )

            if self.api_type == "azure":
                self.client = AsyncAzureOpenAI(
                    base_url=self.base_url,
                    api_key=self.api_key,
                    api_version=self.api_version,
                )
            elif self.api_type == "aws":
                raise ModelNotSupported("aws")
            else:
                self.client = AsyncOpenAI(
                    api_key=self.api_key, 
                    base_url=self.base_url
                )

            # Initialize tokenizer
            try:
                self.tokenizer = tiktoken.encoding_for_model(self.model)
            except KeyError:
                # If the model is not in tiktoken's presets, use cl100k_base as default
                self.tokenizer = tiktoken.get_encoding("cl100k_base")

            self.token_counter = TokenCounter(self.tokenizer)

    # Token 管理
    def count_tokens(self, text: str) -> int:
        """Calculate the number of tokens in a text"""
        return self.token_counter.count_text(text)
    
    def count_message_tokens(self, messages: List[dict]) -> int:
        return self.token_counter.count_message_tokens(messages)
    
    def update_token_count(self, input_tokens: int, completion_tokens: int = 0) -> None:
        """Update token counts"""
        # Only track tokens if max_input_tokens is set
        self.total_input_tokens += input_tokens
        self.total_completion_tokens += completion_tokens
        logger.info(
            f"Token usage: Input={input_tokens}, Completion={completion_tokens}, "
            f"Cumulative Input={self.total_input_tokens}, Cumulative Completion={self.total_completion_tokens}, "
            f"Total={input_tokens + completion_tokens}, Cumulative Total={self.total_input_tokens + self.total_completion_tokens}"
        )   

    def check_token_limit(self, input_tokens: int) -> bool:
        """Check if token limits are exceeded"""
        if self.max_input_tokens is not None:
            return (self.total_input_tokens + input_tokens) <= self.max_input_tokens
        # If max_input_tokens is not set, always return True
        return True

    def get_limit_error_message(self, input_tokens: int) -> str:
        """Generate error message for token limit exceeded"""
        if (
            self.max_input_tokens is not None
            and (self.total_input_tokens + input_tokens) > self.max_input_tokens
        ):
            return f"Request may exceed input token limit (Current: {self.total_input_tokens}, Needed: {input_tokens}, Max: {self.max_input_tokens})"

        return "Token limit exceeded"
    
    @staticmethod
    def format_messages(messages: List[Union[dict, Message]], supports_images: bool = False) -> List[dict]:
        '''将消息（dict 或 Message）转换为 OpenAI 格式。
        参数：
            messages: 可以是dict或Message对象的消息列表
            supports_images: 指示目标模型是否支持图像输入的标志
        返回：
            List[dict]:OpenAI格式的格式化消息列表
        异常：
            ValueError：空响应或格式错误。
            TypeError：消息类型错误
        示例：
            >>> msgs = [
            ...     Message.system_message("You are a helpful assistant"),
            ...     {"role": "user", "content": "Hello"},
            ...     Message.user_message("How are you?")
            ... ]
            >>> formatted = LLM.format_messages(msgs)
        '''
        formatted_messages = []

        for message in messages:
            # 转换 Message 为字典
            if isinstance(message, Message):
                message = message.to_dict()
                if "content" in message or "tool_calls" in message:
                    formatted_messages.append(message)
            elif isinstance(message, dict):
                # If message is a dict, ensure it has required fields
                if "role" not in message:
                    raise ValueError("Message dict must contain 'role' field")
                
                # 处理 base64 图像（若支持）
                if supports_images and message.get("base64_image"):
                    # Initialize or convert content to appropriate format
                    if not message.get("content"):
                        message["content"] = []
                    elif isinstance(message["content"], str):
                        message["content"] = [
                            {"type": "text", "text": message["content"]}
                        ]
                    elif isinstance(message["content"], list):
                        # Convert string items to proper text objects
                        message["content"] = [
                            (
                                {"type": "text", "text": item}
                                if isinstance(item, str)
                                else item
                            )
                            for item in message["content"]
                        ]
                    # 转为 image_url（data:image/jpeg;base64,...）。
                    message["content"].append(
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{message['base64_image']}"
                            },
                        }
                    )
                    # 移除 base64_image 字段
                    del message["base64_image"]
                # 若不支持图像，丢弃 base64_image。
                elif not supports_images and message.get("base64_image"):
                    # Just remove the base64_image field and keep the text content
                    del message["base64_image"]

                # 处理 url 图像（若支持）
                if supports_images and message.get("image_urls"):
                    # Initialize or convert content to appropriate format
                    if not message.get("content"):
                        message["content"] = []
                    elif isinstance(message["content"], str):
                        message["content"] = [
                            {"type": "text", "text": message["content"]}
                        ]
                    elif isinstance(message["content"], list):
                        # Convert string items to proper text objects
                        message["content"] = [
                            (
                                {"type": "text", "text": item}
                                if isinstance(item, str)
                                else item
                            )
                            for item in message["content"]
                        ]
                    # 转为 image_url
                    image_urls_list = message.get("image_urls")
                    if image_urls_list:
                        message["content"].extend(image_urls_list)
                    del message["image_urls"]
                elif not supports_images and message.get("image_urls"):
                    del message["image_urls"]

                # 确保消息包含 content 或 tool_calls。
                if "content" in message or "tool_calls" in message:
                    formatted_messages.append(message)
                # else: do not include the message
            else:
                raise TypeError(f"Unsupported message type: {type(message)}")
            
        # Validate all messages have required fields
        for msg in formatted_messages:
            if msg["role"] not in ROLE_VALUES:
                raise ValueError(f"Invalid role: {msg['role']}")
        return formatted_messages
    

    # 重试：对 OpenAIError、ValueError 等重试 6 次，指数退避。
    async def ask(
        self,
        messages: List[Union[dict, Message]],
        system_msgs: Optional[List[Union[dict, Message]]] = None,
        stream: bool = True,
        temperature: Optional[float] = None,
    ) -> Union[str, AsyncGenerator[str, None]]:
        '''发送消息到 LLM，获取文本响应（支持流式/非流式）。
        Args:
            messages: List of conversation messages
            system_msgs: Optional system messages to prepend
            stream (bool): Whether to stream the response
            temperature (float): Sampling temperature for the response

        Returns:
            str: The generated response

        Raises:
            TokenLimitExceeded: 如果超过令牌限制
            ValueError: 如果消息无效或响应为空
            OpenAIError: 如果重试后API调用失败
            Exception: 针对意外错误
        '''
        try:
            # 检查模型是否支持图像。
            supports_images = self.model in MULTIMODAL_MODELS

            # 格式化消息（合并系统消息和用户消息）。
            formatted_messages = []
            if system_msgs:
                formatted_messages.extend(self.format_messages(system_msgs, supports_images))
            formatted_messages.extend(self.format_messages(messages, supports_images))
            messages = formatted_messages

            # 计算 token，检查限制。
            
            # 设置参数（模型、token 数、温度等）。
            params = {
                "model": self.model,
                "messages": messages
            }
            if self.model in REASONING_MODELS:
                params["max_completion_tokens"] = self.max_tokens
            else:
                params["max_tokens"] = self.max_tokens
                params["temperature"] = (
                    temperature if temperature is not None else self.temperature
                )
            
            # 非流式：直接返回响应内容，更新 token。
            if not stream:
                response = await self.client.chat.completions.create(**params, stream=False)
                if not response.choices or not response.choices[0].message.content:
                    raise ValueError("Empty or invalid response from LLM")
                # Update token counts todo
                return response.choices[0].message.content
            
            # Streaming request
            response = await self.client.chat.completions.create(**params, stream=True)
            
            async def generate_stream() -> AsyncGenerator[str, None]:
                completion_text = ""
                async for chunk in response:
                    chunk_message = chunk.choices[0].delta.content or ""
                    completion_text += chunk_message
                    yield chunk_message
                # After the stream ends, you might want to update token counts
                # self.update_token_count(input_tokens, self.count_tokens(completion_text)) # todo: need input_tokens here
            
            return generate_stream()
        except TokenLimitExceeded:
            # Re-raise token limit errors without logging
            raise
        except ValueError:
            logger.exception(f"Validation error")
            raise
        except OpenAIError as oe:
            logger.exception(f"OpenAI API error")
            if isinstance(oe, AuthenticationError):
                logger.error("Authentication failed. Check API key.")
            elif isinstance(oe, RateLimitError):
                logger.error("Rate limit exceeded. Consider increasing retry attempts.")
            elif isinstance(oe, APIError):
                logger.error(f"API error: {oe}")
            raise
        except Exception:
            logger.exception(f"Unexpected error in ask")
            raise
        return None
    
    async def ask_tool(
        self,
        messages: List[Union[dict, Message]],
        system_msgs: Optional[List[Union[dict, Message]]] = None,
        timeout: int = 300,
        tools: Optional[List[dict]] = None,
        tool_choice: TOOL_CHOICE_TYPE = ToolChoice.AUTO, # type: ignore
        temperature: Optional[float] = None,
        **kwargs,
    ) -> ChatCompletionMessage | None:
        """
        调用 LLM 执行工具/函数，返回 ChatCompletionMessage。

        Args:
            messages: List of conversation messages
            system_msgs: Optional system messages to prepend
            timeout: Request timeout in seconds
            tools: List of tools to use
            tool_choice: Tool choice strategy
            temperature: Sampling temperature for the response
            **kwargs: Additional completion arguments

        Returns:
            ChatCompletionMessage: The model's response

        Raises:
            TokenLimitExceeded: If token limits are exceeded
            ValueError: If tools, tool_choice, or messages are invalid
            OpenAIError: If API call fails after retries
            Exception: For unexpected errors
        """
        try:
            # 验证 tool_choice 和工具格式。
            if tool_choice not in TOOL_CHOICE_VALUES:
                raise ValueError(f"Invalid tool_choice: {tool_choice}")
            # 检查模型是否支持图像。
            supports_images = self.model in MULTIMODAL_MODELS

            # 格式化消息（合并系统消息和用户消息）。
            formatted_messages = []
            if system_msgs:
                formatted_messages.extend(self.format_messages(system_msgs, supports_images))
            formatted_messages.extend(self.format_messages(messages, supports_images))
            messages = formatted_messages

            # 计算 token（包括工具描述）。

            # 设置参数（工具、超时等），强制非流式。
            params = {
                "model": self.model,
                "messages": messages,
                "tools": tools,
                "tool_choice": tool_choice,
                "timeout": timeout,
                **kwargs,
            }
            # 打印参数
            logger.debug(f"ask_tool params: {params}")
            if self.model in REASONING_MODELS:
                params["max_completion_tokens"] = self.max_tokens
            else:
                params["max_tokens"] = self.max_tokens
                params["temperature"] = (
                    temperature if temperature is not None else self.temperature
                )
            params["stream"] = False  # Always use non-streaming for tool requests
            # 返回响应消息（或 None 若无效）。
            response: ChatCompletion = await self.client.chat.completions.create(**params)
            if not response.choices or not response.choices[0].message:
                raise ValueError("Invalid or empty response from LLM")
            # 更新 token todo
            return response.choices[0].message
        except TokenLimitExceeded:
            # Re-raise token limit errors without logging
            raise
        except ValueError as ve:
            logger.error(f"Validation error in ask_tool: {ve}")
            raise
        except OpenAIError as oe:
            logger.error(f"OpenAI API error: {oe}")
            if isinstance(oe, AuthenticationError):
                logger.error("Authentication failed. Check API key.")
            elif isinstance(oe, RateLimitError):
                logger.error("Rate limit exceeded. Consider increasing retry attempts.")
            elif isinstance(oe, APIError):
                logger.error(f"API error: {oe}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in ask_tool: {e}")
            raise
        return None

if __name__ == "__main__":
    llm = LLM("default")
    msgs = [
        {"role": "user", "content": "Hello"},
        Message.assistant_message("Hello! How can I assist you today?"),
        Message.user_message("How are you?"),
        # Message.tool_message("'To test the MCP tool, we can use the Bash function to run a simple command. Let\'s start by running a basic command such as `echo "Hello, MCP!"` to see if the Bash function works as expected.\n\nI will now call the Bash function with this command.'", name="Bash", base64_image="", tool_call_id="'call_1a04046ea80347eda4ecc4'")
    ]
    sys_msgs = [
        Message.system_message("You are a helpful assistant"),
        Message.system_message("additonal system messages"),
    ]

    # 定义异步运行函数
    async def main():
        response = await llm.ask(msgs, sys_msgs, False)
        logger.info(f"llm response: {response}")
    
    # 运行异步函数
    import asyncio
    asyncio.run(main())
