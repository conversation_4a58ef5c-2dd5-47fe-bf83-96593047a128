{"mcpServers": {"filesystem": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/"], "transportType": "stdio"}, "browser_search": {"disabled": false, "timeout": 60, "command": "/opt/ruyi-ai-agent/external-mcp-servers/browser_search/venv/bin/python", "args": ["/opt/ruyi-ai-agent/external-mcp-servers/browser_search/mcp_server.py"], "transport": "stdio"}}}