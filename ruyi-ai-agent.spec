%define __brp_strip %{nil}
%define debug_package %{nil}
%define __arch_install_post %{nil}

%global app_dir /opt/ruyi-ai-agent

Name:           ruyi-ai-agent
Version:        1.1.0
Release:        4%{?dist}
Summary:        Ruyi AI Agent with MCP Server and System Tools
License:        MIT
URL:            https://github.com/yourusername/ruyi-ai-agent
Source0:        %{name}.tar.gz

BuildArch:      riscv64
BuildRequires:  python3-devel
BuildRequires:  python3-pip
BuildRequires:  rfkill
BuildRequires:  dbus-python
BuildRequires:  gcc
BuildRequires:  make

Requires:       python3
Requires:       python3-pip
Requires:       sqlite
Requires:       NetworkManager
Requires:       pulseaudio
Requires:       bluez
Requires:       rfkill
Requires:       activation-service >= 0.0.1-19

%description
Ruyi AI Agent is an intelligent agent system that provides various system control
capabilities through a Model Context Protocol (MCP) server. It includes tools for
controlling system volume, brightness, bluetooth, network, and other system functions.

%prep
%setup -q -n %{name}-%{version} -c

%build
pip install --extra-index-url https://rvbookpip:<EMAIL>/repository/pip-group/simple --trusted-host nexus-console.risc-verse.cn -i https://pypi.tuna.tsinghua.edu.cn/simple --target=%{_builddir}/%{name}-%{version}/vendor -r requirements.txt

echo "Attempting to remove pyarrow from vendor directory..."
if [ -d %{_builddir}/%{name}-%{version}/vendor/pyarrow ]; then
    rm -rf %{_builddir}/%{name}-%{version}/vendor/pyarrow
    rm -rf %{_builddir}/%{name}-%{version}/vendor/pyarrow-*.dist-info
    echo "Removed pyarrow."
fi


%install

mkdir -p %{buildroot}/opt/ruyi-ai-agent
cp -r {app,config,mcpserver,tests,main.py,pip.conf,README.md,requirements.txt,ruyi-ai-agent,ruyi-ai-agent.desktop} %{buildroot}/opt/ruyi-ai-agent/
cp -a ./* %{buildroot}%{app_dir}/



# Install system scripts
mkdir -p %{buildroot}/usr/local/bin
install -m 755 app/tool/scripts/brightness_set.sh %{buildroot}/usr/local/bin/brightness_set.sh


# Install autostart desktop file

mkdir -p %{buildroot}%{_sysconfdir}/xdg/autostart/

# Create symlink in autostart directory - This comment is wrong, it's copying the file
# Use install for both locations

install -m644 ruyi-ai-agent.desktop %{buildroot}%{_sysconfdir}/xdg/autostart/ruyi-ai-agent.desktop


mkdir -p %{buildroot}%{_bindir}
mkdir -p %{buildroot}/etc/skel/.config/ruyi-ai/
install -m755 ruyi-ai-agent %{buildroot}%{_bindir}/ruyi-ai-agent
install -m 644 config/mcp.json %{buildroot}/etc/skel/.config/ruyi-ai/mcp.json

%files
/opt/ruyi-ai-agent/
%{_bindir}/ruyi-ai-agent
/usr/local/bin/brightness_set.sh
%{_sysconfdir}/xdg/autostart/ruyi-ai-agent.desktop
%{_sysconfdir}/skel/.config/ruyi-ai/mcp.json



%changelog
* Tue Jun 16 2025 zhangxuyang <<EMAIL>> - 1.0.0-11
- optimize prompt

* Wed Jun 13 2025 zhangxuyang <<EMAIL>> - 1.0.0-8
- set env before run mcpserver

* Wed Jun 12 2025 zhangxuyang <<EMAIL>> - 1.0.0-5
- fix environment $DISPLAY not set error

* Tue Jun 11 2025 zhangxuyang <<EMAIL>> - 1.0.0-4
- fix os tools dependency not found

* Thu May 22 2025 zhangxuyang <<EMAIL>> - 1.0.0-1
- Initial package release
