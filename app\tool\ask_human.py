from app.tool.base_tool import <PERSON><PERSON>ool
from typing import Dict, Optional
from app.tool.tool_schema import ToolResult

class AskHuman(BaseTool):
    """Add a tool to ask human for help."""

    name: str = "ask_human"
    description: str = "Use this tool to ask human for help."
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "inquire": {
                "type": "string",
                "description": "The question you want to ask human.",
            }
        },
        "required": ["inquire"],
    }

    async def execute(self, **kwargs) -> ToolResult:
        inquire = kwargs.get("inquire", "")
        if not inquire:
            return Tool<PERSON><PERSON>ult(error="No question provided to ask human.")
        return ToolResult(
            output=f'{inquire}\n\n',
            status='waiting_for_human_input'
        )
