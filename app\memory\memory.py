
from typing import List, Optional
from pydantic import BaseModel, Field

from app.schema import ROLE_TYPE

from app.logger import logger
from app.schema import Message


class Memory(BaseModel):
    '''管理对话历史（消息列表）'''
    messages: List[Message] = Field(default_factory=list)
    max_messages: int = Field(default=100)
    metadata: dict = Field(default_factory=dict)

    def add_message(self, message: Message) -> None:
        """Add a message to memory"""
        self.messages.append(message)
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]

    def add_messages(self, messages: List[Message]) -> None:
        """Add multiple messages to memory"""
        self.messages.extend(messages)
        # Optional: Implement message limit
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages :]

    def clear(self) -> None:
        """Clear all messages"""
        self.messages.clear()

    def get_recent_messages(self, n: int) -> List[Message]:
        """Get n most recent messages"""
        return self.messages[-n:]
    
    def to_dict_list(self) -> List[dict]:
        """Convert messages to list of dicts"""
        return [msg.to_dict() for msg in self.messages]
    
    def update_memory(self,
        role: ROLE_TYPE, # type: ignore
        content: str,
        base64_image: Optional[str] = None,
        image_urls: Optional[List] = None,
        **kwargs
    ) -> None:
        """
        将消息添加到代理的内存中
        args:
            role：消息发送者的角色（用户、系统、助理、工具）。
            content：消息内容。
            base64_image：可选的base64编码图像。
            **kwargs：其他参数（例如，工具消息的tool_call_id）。
        raises:
            ValueError:如果角色不受支持。
        """
        message_map = {
            "user": Message.user_message,
            "system": Message.system_message,
            "assistant": Message.assistant_message,
            "tool": Message.tool_message
        }
        if role not in message_map:
            raise ValueError(f"Unsupported message role: {role}")
        # 根据角色选择 kwargs
        if role == "tool":
            msg_kwargs = {
                "base64_image": base64_image,
                **kwargs
            }
        elif role == "system":
            msg_kwargs = {}
        elif role == "assistant":
            msg_kwargs = {
                "base64_image": base64_image
            }
        else:   # "user"
            msg_kwargs = {
                "base64_image": base64_image,
                "image_urls": image_urls
            }
        msg_factory = message_map[role]
        msg = msg_factory(content, **msg_kwargs)
        self.add_message(msg)

if __name__ == "__main__":
    memory = Memory()
    memory.update_memory("system", "this is system messages")
    memory.update_memory("user", "this is user messages")
    memory.update_memory("tool", "this is user messages", name="bash", base64_image="image_data", tool_call_id="123")
    memory.update_memory("system", "additonal system messages")
    logger.info(memory.to_dict_list())
