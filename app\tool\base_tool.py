from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field

from app.tool.tool_schema import ToolResult

class BaseTool(ABC, BaseModel):
    '''定义工具的通用结构。
    抽象基类，继承 ABC（抽象类）和 pydantic.BaseModel（数据验证）。
    '''
    name: str
    description: str
    parameters: Optional[Dict] = None

    class Config:
        arbitrary_types_allowed = True
        
    async def __call__(self, **kwargs) -> ToolResult:
        """特殊方法，使对象可调用（如 tool(**kwargs)）
        逻辑: 代理 execute 方法，异步执行工具。
        """
        return await self.execute(**kwargs)

    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """工具的执行逻辑，接收任意关键字参数，返回任意类型。
        定义: 抽象方法，子类必须实现。
        """
    
    def to_param(self) -> Dict[str, Any]:
        """将工具转换为 OpenAI 工具调用格式（JSON）。
        """
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters,
            }
        }
    
    def to_brief(self) -> Dict[str, str]:
        """将工具转换为简要描述(JSON)"""
        return {
            "name": self.name,
            "description": self.description
        }