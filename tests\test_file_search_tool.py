import unittest
import os
import shutil
import sqlite3
import time

# Add the parent directory to the path to import modules
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.tool.file_search_tool import indexer
from app.tool.file_search_tool import searcher

class TestFileSearchTool(unittest.TestCase):

    def setUp(self):
        """Set up a temporary directory and database for testing."""
        self.test_dir = "test_root"
        os.makedirs(self.test_dir, exist_ok=True)
        
        # Ensure a clean database for each test
        self.db_path = os.path.join(os.path.dirname(os.path.abspath(indexer.__file__)), indexer.DATABASE_NAME)
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def tearDown(self):
        """Clean up the temporary directory and database after testing."""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

    def _create_dummy_files(self):
        """Create some dummy files and directories for testing."""
        # File 1: text file
        with open(os.path.join(self.test_dir, "file1.txt"), "w") as f:
            f.write("This is a test file with some content.")
        
        # File 2: Python file
        with open(os.path.join(self.test_dir, "script.py"), "w") as f:
            f.write("import os\nprint('Hello World')")
        
        # Directory 1
        os.makedirs(os.path.join(self.test_dir, "subdir1"), exist_ok=True)
        
        # File 3: inside subdir1
        with open(os.path.join(self.test_dir, "subdir1", "document.md"), "w") as f:
            f.write("# Markdown Document\nThis is a markdown file.")
        
        # File 4: large file (simulated)
        with open(os.path.join(self.test_dir, "large_file.bin"), "wb") as f:
            f.seek(1024 * 1024 - 1) # 1MB
            f.write(b"\0")

    def test_indexing_and_basic_search(self):
        """Test initial indexing and basic search functionalities."""
        self._create_dummy_files()
        indexer.main(self.test_dir)

        # Test basic search by filename
        results = searcher.search_files(filename_keyword="file1")
        self.assertEqual(len(results), 1)
        self.assertEqual(os.path.basename(results[0]['filepath']), "file1.txt")

        # Test search by extensions
        results = searcher.search_files(extensions=[".py"])
        self.assertEqual(len(results), 1)
        self.assertEqual(os.path.basename(results[0]['filepath']), "script.py")

        # Test search by text content
        results = searcher.search_files(text_content_keyword="test file")
        self.assertEqual(len(results), 1)
        self.assertEqual(os.path.basename(results[0]['filepath']), "file1.txt")

        # Test search for directory
        results = searcher.search_files(filename_keyword="subdir1", is_dir=True)
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0]['is_dir'])

        # Test search for large file
        results = searcher.search_files(filename_keyword="large_file", min_size=1024*100)
        self.assertEqual(len(results), 1)
        self.assertEqual(os.path.basename(results[0]['filepath']), "large_file.bin")

    def test_incremental_indexing(self):
        """Test incremental indexing: add, modify, delete files."""
        self._create_dummy_files()
        indexer.main(self.test_dir) # Initial index

        # Verify initial count
        conn = searcher.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        initial_count = cursor.fetchone()[0]
        conn.close()
        # Expecting 4 files + 1 directory = 5 entries
        self.assertEqual(initial_count, 5) 

        # --- Test adding a new file ---
        with open(os.path.join(self.test_dir, "new_file.txt"), "w") as f:
            f.write("This is a new file.")
        indexer.main(self.test_dir) # Re-index

        results = searcher.search_files(filename_keyword="new_file")
        self.assertEqual(len(results), 1)
        
        conn = searcher.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        new_count = cursor.fetchone()[0]
        conn.close()
        self.assertEqual(new_count, initial_count + 1) # Should be 6 now

        # --- Test modifying an existing file ---
        with open(os.path.join(self.test_dir, "file1.txt"), "w") as f:
            f.write("This is MODIFIED content.")
        indexer.main(self.test_dir) # Re-index

        results = searcher.search_files(filename_keyword="file1", text_content_keyword="MODIFIED")
        self.assertEqual(len(results), 1)
        
        conn = searcher.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        modified_count = cursor.fetchone()[0]
        conn.close()
        self.assertEqual(modified_count, new_count) # Count should remain 6

        # --- Test deleting a file ---
        os.remove(os.path.join(self.test_dir, "script.py"))
        indexer.main(self.test_dir) # Re-index

        results = searcher.search_files(extensions=[".py"]) # Changed to extensions
        self.assertEqual(len(results), 0) # Should be deleted

        conn = searcher.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        deleted_count = cursor.fetchone()[0]
        conn.close()
        self.assertEqual(deleted_count, modified_count - 1) # Should be 5 now

        # --- Test deleting a directory ---
        shutil.rmtree(os.path.join(self.test_dir, "subdir1"))
        indexer.main(self.test_dir) # Re-index

        results = searcher.search_files(filename_keyword="subdir1", is_dir=True)
        self.assertEqual(len(results), 0) # Should be deleted

        conn = searcher.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM files")
        deleted_dir_count = cursor.fetchone()[0]
        conn.close()
        # subdir1 and document.md inside it should be removed
        self.assertEqual(deleted_dir_count, deleted_count - 2) # Should be 3 now (file1.txt, large_file.bin, new_file.txt)

    def test_time_based_search(self):
        """Test searching by creation and modification times."""
        # Create files with specific times
        file_old_path = os.path.join(self.test_dir, "old_file.txt")
        file_recent_path = os.path.join(self.test_dir, "recent_file.txt")

        # Create an old file (e.g., 2 days ago)
        with open(file_old_path, "w") as f:
            f.write("Old content")
        old_time_int = int(time.time() - (2 * 24 * 60 * 60))
        os.utime(file_old_path, (old_time_int, old_time_int)) # Set access and modification times

        # Create a recent file (e.g., 1 hour ago)
        with open(file_recent_path, "w") as f:
            f.write("Recent content")
        recent_time_int = int(time.time() - (1 * 60 * 60))
        os.utime(file_recent_path, (recent_time_int, recent_time_int))

        indexer.main(self.test_dir) # Index the files

        # Search for files modified in the last 24 hours
        # Use integer timestamps for search
        one_day_ago_int = int(time.time() - (24 * 60 * 60))
        results = searcher.search_files(min_modified_time=one_day_ago_int)
        self.assertEqual(len(results), 1)
        self.assertEqual(os.path.basename(results[0]['filepath']), "recent_file.txt")
        print(f"DEBUG: recent_file.txt last_modified: {results[0]['last_modified']}")
        print(f"DEBUG: one_day_ago_int: {one_day_ago_int}")

        # Search for files modified between old_time and recent_time
        # Adjust boundaries to be inclusive with integer timestamps
        results = searcher.search_files(min_modified_time=old_time_int, max_modified_time=recent_time_int)
        self.assertEqual(len(results), 2) # Should find both old_file and recent_file
        print(f"DEBUG: old_file.txt last_modified: {results[0]['last_modified']}")
        print(f"DEBUG: recent_file.txt last_modified: {results[1]['last_modified']}")
        print(f"DEBUG: Search range: {old_time_int} to {recent_time_int}")

if __name__ == '__main__':
    unittest.main()
