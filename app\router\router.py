
import json
from typing import Dict
from pydantic import BaseModel, Field

from app.agent.base_agent import BaseAgent
from app.agent.tool_call_agent import ToolCallAgent
from app.logger import logger
from app.llm.llm import LLM
from app.prompt.router import SYSTEM_PROMPT, USER_PROMPT
from app.schema import Message


class Router(BaseModel):
    """
    分析用户输入，判断任务类型，分发到合适的Agent处理。
    """
    llm: LLM = Field(default_factory=LLM, description="语言模型实例")
    agents: Dict[str, BaseAgent] = None      # 代理字典，键为代理名称，值为 BaseAgent 实例。

    class Config:
        arbitrary_types_allowed = True      # 允许任意类型（如 LLM、Memory）。
        extra = "allow"                     # 接受子类额外字段，增强扩展性。

    async def route(self, request: str) -> tuple:
        try:
            agent_info = ""
            for name, agent in self.agents.items():
                if isinstance(agent, ToolCallAgent):
                    agent_info += f"- {name}: {agent.description}, \n可用工具: \n{agent.available_tools.to_brief()}"
                    agent_info += "\n"
                elif isinstance(agent, BaseAgent):
                    agent_info += f"- {name}: {agent.description}"
                    agent_info += "\n"

            system_message = Message.system_message(SYSTEM_PROMPT.format(agent_info=agent_info))
            logger.info(f"Router, system_message:{system_message}")
            user_message = Message.user_message(USER_PROMPT.format(request=request))
            logger.info(f"Router, user_message:{user_message}")
            response = await self.llm.ask(system_msgs=[system_message],
                                          messages=[user_message],
                                          stream=False)
            logger.info(f"LLM route completed:{response}")

            # 解析 LLM 响应
            route_result = json.loads(response)
            task_type = route_result.get("task_type")
            agent_name = route_result.get("agent_name")
            if not task_type or task_type not in ["simple", "complex"]:
                raise ValueError(f"Invalid task type: {task_type}")
            if not agent_name or agent_name not in self.agents:
                raise ValueError(f"Invalid agent name: {agent_name}")
            logger.info(f"Routed to {agent_name} for {task_type} task")
            return task_type, agent_name
        except Exception as e:
            logger.error(f"Error route request with LLM:{e}")
            # 默认路由到 Commander Agent 处理复杂任务
            default_agent = "commander"
            logger.info(f"Fallback to default agent: {default_agent}")
            return "complex", default_agent

