
from typing import Dict, Optional
from app.tool.base_tool import BaseTool
from app.tool.tool_schema import ToolResult


_TERMINATE_DESCRIPTION = """Terminate the interaction when the request is met OR if the assistant cannot proceed further with the task.
When you have finished all the tasks, call this tool to end the work."""

class Terminate(BaseTool):
    """当请求得到满足或助手无法继续执行任务时，调用此工具结束工作。"""
    name: str = "Terminate"
    description: str = _TERMINATE_DESCRIPTION
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "status": {
                "type": "string",
                "description": "The finish status of the interaction.",
                "enum": ["success", "failure"]
            }
        },
        "required": ["status"],
    }

    async def execute(self, status: str) -> str:
        return f"The interaction has been completed with status: {status}"