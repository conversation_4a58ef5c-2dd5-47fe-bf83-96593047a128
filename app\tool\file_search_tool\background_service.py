import os
import sys
import time
import signal
import threading
from typing import Optional
from pathlib import Path

from app.logger import logger
from app.config import config
from .file_watcher import start_file_watcher, stop_file_watcher, get_watcher_status
from .indexer import main as run_initial_index


class FileWatcherService:
    """Background service manager for the file watcher"""
    
    def __init__(self):
        self.is_running = False
        self.shutdown_event = threading.Event()
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.shutdown()
    
    def _run_initial_indexing(self):
        """Run initial indexing if database doesn't exist or is empty"""
        try:
            from .indexer import DATABASE_NAME
            # 确保使用绝对路径
            home_dir = os.environ.get("HOME", os.path.expanduser("~"))
            root_dir = os.path.abspath(home_dir)
    
            # 创建数据库目录路径
            db_dir = os.path.join(root_dir, ".local/share/file_search")
            db_path = os.path.join(db_dir, DATABASE_NAME)
            
            # Check if database exists and has data
            if not os.path.exists(db_path):
                logger.info("Database doesn't exist, running initial indexing...")
                self._perform_initial_index()
            else:
                import sqlite3
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM files")
                count = cursor.fetchone()[0]
                conn.close()
                
                if count == 0:
                    logger.info("Database is empty, running initial indexing...")
                    self._perform_initial_index()
                else:
                    logger.info(f"Database already contains {count} files")
                    
        except Exception as e:
            logger.error(f"Error checking database: {e}")
    
    def _perform_initial_index(self):
        """Perform initial indexing"""
        try:
            watcher_config = config.file_watcher_config
            if watcher_config and watcher_config.watch_paths:
                for watch_path in watcher_config.watch_paths:
                    if os.path.exists(watch_path):
                        logger.info(f"Running initial indexing for: {watch_path}")
                        run_initial_index(watch_path)
            else:
                # Default to home directory
                home_dir = os.path.expanduser("~")
                logger.info(f"Running initial indexing for: {home_dir}")
                run_initial_index(home_dir)
                
        except Exception as e:
            logger.error(f"Error during initial indexing: {e}")
    
    def start(self, run_initial_index: bool = True):
        """Start the file watcher service"""
        if self.is_running:
            logger.warning("File watcher service is already running")
            return
        
        logger.info("Starting file watcher service...")
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        try:
            # Run initial indexing if requested
            if run_initial_index:
                self._run_initial_indexing()
            
            # Start the file watcher
            start_file_watcher()
            
            self.is_running = True
            logger.info("File watcher service started successfully")
            
            # Keep the service running
            self._run_service_loop()
            
        except Exception as e:
            logger.error(f"Error starting file watcher service: {e}")
            self.shutdown()
    
    def _run_service_loop(self):
        """Main service loop"""
        try:
            while self.is_running and not self.shutdown_event.is_set():
                # Log status periodically
                status = get_watcher_status()
                if status['is_running']:
                    logger.debug(f"File watcher status: {status}")
                else:
                    logger.warning("File watcher is not running, attempting to restart...")
                    try:
                        start_file_watcher()
                    except Exception as e:
                        logger.error(f"Failed to restart file watcher: {e}")
                
                # Wait for shutdown signal or timeout
                if self.shutdown_event.wait(timeout=60):  # Check every minute
                    break
                    
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Error in service loop: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """Shutdown the service"""
        if not self.is_running:
            return
        
        logger.info("Shutting down file watcher service...")
        self.is_running = False
        self.shutdown_event.set()
        
        try:
            stop_file_watcher()
            logger.info("File watcher service stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping file watcher: {e}")
    
    def get_status(self):
        """Get service status"""
        watcher_status = get_watcher_status()
        return {
            'service_running': self.is_running,
            'watcher_status': watcher_status
        }


def main():
    """Main entry point for the service"""
    import argparse
    
    parser = argparse.ArgumentParser(description='File Watcher Background Service')
    parser.add_argument('--no-initial-index', action='store_true', 
                       help='Skip initial indexing on startup')
    parser.add_argument('--config-path', type=str, 
                       help='Path to configuration file')
    
    args = parser.parse_args()
    
    # Initialize service
    service = FileWatcherService()
    
    try:
        # Start the service
        service.start(run_initial_index=not args.no_initial_index)
    except KeyboardInterrupt:
        logger.info("Service interrupted by user")
    except Exception as e:
        logger.error(f"Service error: {e}")
    finally:
        service.shutdown()


if __name__ == '__main__':
    main()
