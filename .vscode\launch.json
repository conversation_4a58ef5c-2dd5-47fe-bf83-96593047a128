{
    "version": "0.2.0",
    "configurations": [

        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}",  // 设置工作目录为项目根
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Agent: run_mcp_server",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/mcpserver/mcp_server.py",
            "console": "integratedTerminal",
            "args": [
                "--transport",
                "stdio"
            ],
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Agent: main.py",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/main.py",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        // {
        //     "name": "Agent: run_mcp.py",
        //     "type": "debugpy",
        //     "request": "launch",
        //     "program": "${workspaceFolder}/run_mcp.py",
        //     "console": "integratedTerminal",
        //     "args": [
        //         "--connection",
        //         "stdio"
        //     ],
        //     "justMyCode": true
        // }
    ]
}