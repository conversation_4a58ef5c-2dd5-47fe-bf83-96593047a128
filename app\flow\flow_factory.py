# from enum import Enum
# import sys
# from typing import Dict, List, Union

# from app.config import config
# from app.agent.base_agent import BaseAgent
# from app.agent.mcp_agent import MCPAgent
# from app.flow.base_flow import BaseFlow
# from app.flow.planning_flow import PlanningFlow

# class FlowType(str, Enum):
#     PLANNING = "planning"

# class FlowFactory:
#     """工厂类，根据类型创建流程（如 PlanningFlow）。"""

#     @staticmethod
#     def create_flow(
#         flow_type: FlowType,
#         agents: Union[BaseAgent, List[BaseAgent], Dict[str, BaseAgent]],
#         **kwargs,
#     ) -> BaseFlow:
#         flows = {
#             FlowType.PLANNING: PlanningFlow,
#         }
#         flow_class = flows.get(flow_type)
#         if not flow_class:
#             raise ValueError(f"Unknown flow type: {flow_type}")

#         return flow_class(agents, **kwargs)

# if __name__ == "__main__":
#     async def main():
#         mcpagent = MCPAgent()
#         # 初始化 MCP 连接
#         await mcpagent.initialize(
#             connection_type="stdio",
#             command=sys.executable,
#             args=["-m", config.mcp_config.server_reference, "--transport", "stdio"],
#         )
#         # 验证工具和记忆
#         print("Tools:", list(mcpagent.available_tools.tool_map.keys()))
#         print("Messages:", [msg.content for msg in mcpagent.memory.messages])

#         agents = {
#             "mcpagent": mcpagent,
#         }

#         flow = FlowFactory.create_flow(
#             flow_type=FlowType.PLANNING,
#             agents=agents,
#         )

#         prompt = "执行MCP工具demo"
#         result = await flow.execute(prompt)
#         print(result)

#     import asyncio
#     asyncio.run(main())