import argparse
import sys
import os
import platform
import subprocess

LIBS_DIR = "/opt/ruyi-ai-agent/vendor/"
if LIBS_DIR not in sys.path:
    sys.path.insert(0, LIBS_DIR)
    print(f"Added {LIBS_DIR} to sys.path")
    print(f"Current sys.path: {sys.path}")

from inspect import Parameter, Signature
import json
from typing import Any, Dict, Optional
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.resources import DirectoryResource
from mcp.server.fastmcp.server import Context # 尽管 MCPServer 不再继承 Context，但 FastMCP 内部可能仍需要它，所以保留导入

from pathlib import Path
from pydantic import AnyUrl

from app.logger import logger
from app.tool.base_tool import BaseTool
from app.tool.browser_use_tool import BrowserUseTool
from app.tool.os_tools import *
from app.tool.ask_human import AskHuman
from app.tool.terminate_tool import Terminate


directory_resource = DirectoryResource(  
    uri=AnyUrl("dir://usr/share/applications/"),  
    name="我的文件",  
    path=Path("/usr/share/applications/").resolve(),  
    recursive=True,  # 递归列出所有文件  
    pattern="*.desktop",  # 可选：只列出desktop文件 
)  


# MCPServer 类
class MCPServer:
    """
    实现 MCP（Model Context Protocol）服务器，管理工具并通过 FastMCP 与客户端通信。
        * 作用: 提供工具服务，响应客户端
        * 核心类: MCPServer，负责工具注册、执行和资源清理。
        * 依赖: FastMCP（MCP 协议实现）、标准工具（如 Bash、Terminate）。
    """

    # 初始化
    def __init__(self, name: str = "Agent MCP Server"):
        """
        设置服务器和工具基础。
            * 创建 FastMCP 实例，命名服务器。
            * 初始化工具字典，添加默认工具。
        """
        # 创建 FastMCP 实例，命名服务器。
        self.server = FastMCP(name)
        # 初始化工具字典
        # self.tools: Dict[str, BaseTool] = {}
        self.tools: Dict[str: Dict[str, BaseTool]] = {}

        # 添加默认工具：
        # self.tools["bash"] = BashTool()
        # self.tools["terminal"] = Terminate()
        # self.tools["volume"] = VolumeControlTool()
        # self.tools["brightness"] = BrightnessControlTool()
        # self.tools["open_apps"] = OpenAppsTool()
        # self.tools["bluetooth"] = BluetoothControlTool()
        # self.tools["network"] = NetworkTool()
        # self.tools["battery"] = BatteryInfoTool()
        # # BrowserUseTool: 浏览器操作。
        # self.tools["browser"] = BrowserUseTool()
        self.tools = {
            # OSAgent工具
            "os_agent": {
                "Bash": BashTool(),
                "AskHuman": AskHuman(),
                "VolumeControl": VolumeControlTool(),
                "BrightnessControl": BrightnessControlTool(),
                "OpenApps": OpenAppsTool(),
                "CloseApps": CloseAppsTool(),
                "BluetoothControl": BluetoothControlTool(),
                "Network": NetworkTool(),
                "BatteryInfo": BatteryInfoTool(),
                "FileSearch": FileSearchTool(),
                "Terminate": Terminate(),
                "Time": TimeTool(),
                "Shutdown": SystemShutdownTool(),
                "Reboot": SystemRebootTool()
            }
        }


    # 工具注册
    def register_tool(self, tool: BaseTool, method_name: Optional[str] = None, agent_name: Optional[str] = None) -> None:
        """将工具暴露为服务器接口。
        作用: 将 BaseTool 实例注册到 MCP 服务器，使其可被客户端调用。
        参数:
            tool: BaseTool 实例，包含工具逻辑。
            method_name: 可选的工具别名，默认使用 tool.name。
        """
        # 工具名称和参数
        # 使用 method_name 或 tool.name 作为工具名。
        base_tool_name = method_name or tool.name
        # 拼接 agent_name 和 tool_name，使用 : 分隔
        tool_name = f"{agent_name}:{base_tool_name}" if agent_name else base_tool_name

        # 调用 tool.to_param() 获取工具的 JSON Schema（如名称、描述、参数）。
        tool_param = tool.to_param()
        # 提取 function 部分，包含工具元数据。
        tool_function = tool_param["function"]
        # 更新 tool_function 的 name 字段为拼接后的名称
        tool_function["name"] = tool_name

        # 创建异步函数 tool_method，包装工具执行。
        async def tool_method(**kwargs):
            logger.info(f"Executing {tool_name}: {kwargs}")
            result = await tool.execute(**kwargs)
            logger.info(f"Result of {tool_name}: {result}")
            # 处理返回结果（Pydantic 模型或字典转为 JSON）。
            if hasattr(result, "model_dump"):
                return json.dumps(result.model_dump())
            elif isinstance(result, dict):
                return json.dumps(result)
            return result
        # 设置函数元数据（名称、文档、签名）。
        tool_method.__name__ = tool_name
        # 显式设置工具描述
        tool_method.__doc__ = self._build_docstring(tool_function)
        tool_method.__signature__ = self._build_signature(tool_function)
        param_props = tool_function.get("parameters", {}).get("properties", {})
        required_params = tool_function.get("parameters", {}).get("required", [])
        tool_method._parameter_schema = {
            param_name: {
                "description": param_details.get("description", ""),
                "type": param_details.get("type", "any"),
                "required": param_name in required_params,
            }
            for param_name, param_details in param_props.items()
        }
        # 通过 self.server.tool() 注册到 FastMCP。
        # 将 tool_method 绑定到 MCP 服务器，客户端可通过协议调用（如 {"name": "bash", "arguments": {...}}）。
        self.server.tool()(tool_method)

    # 文档生成
    '''BaseTool类
    {
        "type": "function",
        "function": {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
        }
    }
    '''
    def _build_docstring(self, tool_function: dict) -> str:
        """
        提供工具参数说明，增强可读性。
        """
        description = tool_function.get("description", "")
        param_props = tool_function.get("parameters", {}).get("properties", {})
        required_params = tool_function.get("parameters", {}).get("required", [])

        # Build docstring (match original format)
        docstring = description
        if param_props:
            docstring += "\n\nParameters:\n"
            for param_name, param_details in param_props.items():
                required_str = (
                    "(required)" if param_name in required_params else "(optional)"
                )
                param_type = param_details.get("type", "any")
                param_desc = param_details.get("description", "")
                docstring += (
                    f"    {param_name} ({param_type}) {required_str}: {param_desc}\n"
                )

        return docstring

    # 签名生成
    def _build_signature(self, tool_function: dict) -> Signature:
        """
        根据工具参数生成 Python 函数签名，映射 JSON Schema 类型到 Python 类型。
        """
        param_props = tool_function.get("parameters", {}).get("properties", {})
        required_params = tool_function.get("parameters", {}).get("required", [])

        parameters = []

        # Follow original type mapping
        for param_name, param_details in param_props.items():
            param_type = param_details.get("type", "")
            default = Parameter.empty if param_name in required_params else None

            # Map JSON Schema types to Python types (same as original)
            annotation = Any
            if param_type == "string":
                annotation = str
            elif param_type == "integer":
                annotation = int
            elif param_type == "number":
                annotation = float
            elif param_type == "boolean":
                annotation = bool
            elif param_type == "object":
                annotation = dict
            elif param_type == "array":
                annotation = list

            # Create parameter with same structure as original
            param = Parameter(
                name=param_name,
                kind=Parameter.KEYWORD_ONLY,
                default=default,
                annotation=annotation,
            )
            parameters.append(param)

        return Signature(parameters=parameters)

    # 清理
    async def cleanup(self) -> None:
        """
        针对浏览器工具的特殊处理。
        """

    def register_all_tools(self) -> None:
        """Register all tools with the server."""
        # for tool in self.tools.values():
        #     self.register_tool(tool)
        for agent_name, tool_dict in self.tools.items():
            for tool_name, tool in tool_dict.items():
                self.register_tool(tool, method_name=tool_name, agent_name=agent_name)

    # 运行
    def run(self, transport: str) -> None:
        """
        启动服务器，响应客户端。
        """
        # 注册所有工具。
        self.register_all_tools()
        # 设置退出时清理。

        # 启动 FastMCP（默认 stdio）。
        logger.info(f"Starting Agent MCP Server ({transport} mode)")
        self.server.run(transport=transport)

def set_xfce_environ_vars():
    """
    从 xfce4-session 进程的环境变量中读取并设置到当前进程。
    仅在 Linux 系统上执行。
    """
    if platform.system() == "Linux":
        try:
            # 查找 xfce4-session 进程的 PID
            pid_output = subprocess.check_output(["pgrep", "xfce4-session"], text=True).strip()
            if not pid_output:
                logger.warning("xfce4-session process not found. Skipping environment variable setup.")
                return

            pid = pid_output.split('\n')[0] # 取第一个PID
            environ_path = f"/proc/{pid}/environ"

            if not os.path.exists(environ_path):
                logger.warning(f"Environment file not found: {environ_path}. Skipping environment variable setup.")
                return

            with open(environ_path, 'rb') as f:
                environ_data = f.read()

            # 环境变量以 null 字符分隔
            env_vars = environ_data.split(b'\0')
            for env_var in env_vars:
                if b'=' in env_var:
                    key, value = env_var.split(b'=', 1)
                    try:
                        os.environ[key.decode('utf-8')] = value.decode('utf-8')
                        logger.debug(f"Set environment variable: {key.decode('utf-8')}={value.decode('utf-8')}")
                    except UnicodeDecodeError:
                        logger.warning(f"Could not decode environment variable: {env_var}. Skipping.")
        except FileNotFoundError:
            logger.error("pgrep command not found. Please ensure pgrep is installed.")
        except subprocess.CalledProcessError as e:
            logger.error(f"Error executing pgrep: {e}")
        except Exception as e:
            logger.error(f"An unexpected error occurred while setting xfce environment variables: {e}")
    else:
        logger.info("Not a Linux system. Skipping xfce4-session environment variable setup.")
        
def parse_args() -> argparse.Namespace:
    """
    解析 --transport 参数，仅支持 stdio。
    """
    # 创建解析器：初始化 ArgumentParser，设置程序描述为 "Agent MCP Server"。
    parser = argparse.ArgumentParser(description="Agent MCP Serverr")
    """添加参数:
    参数:
        --transport: 参数名，长格式（也可写 -t 推测，但未定义短格式）。
        choices=["stdio"]: 限制输入值，仅允许 "stdio"。
        default="stdio": 默认值，若未提供参数则使用 "stdio"。
        help: 帮助文本，描述参数作用。
    """
    parser.add_argument("--transport",
                        default="stdio",
                        choices=["stdio"],
                        help="服务器通信方式: stdio or http (default: stdio)")
    
    """解析命令行输入，返回 Namespace 对象。
    示例:
        命令：python script.py --transport stdio
        输出：Namespace(transport='stdio')
        命令：python script.py
        输出：Namespace(transport='stdio')（默认值）
    """
    return parser.parse_args()
    
if __name__ == "__main__":
    # 在服务器启动前设置环境变量
    set_xfce_environ_vars()
    # 调用 parse_args，获取命令行参数：args 是 Namespace 对象，含 transport 属性。
    args = parse_args()
    server = MCPServer()
    # 调用 server.run，传入解析的 transport 值。
    server.run(transport=args.transport)