
from typing import Dict
from app.memory.memory import Memory
from app.memory.session_storage import SessionStorage


class PostgreSessionStorage(SessionStorage):
    """PostgreSQL存储实现，管理用户会话历史"""

    def __init__(self):
        # 连接数据库
        pass

    def get_session(self, user_id: str) -> Memory:
        """根据用户id获取历史会话"""
        return  Memory()

    def save_session(self, user_id: str, memory: Memory) -> None:
        """保存用户会话历史"""
        pass

    def clead_session(self, user_id: str) -> None:
        """清除用户会话历史"""
        pass