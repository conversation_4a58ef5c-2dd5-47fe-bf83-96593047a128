# File Watcher System

This document describes the file watcher system that automatically monitors file changes and updates the file search database in real-time.

## Overview

The file watcher system provides automatic file indexing by monitoring specified directories for file changes (create, modify, delete, move) and updating the SQLite database accordingly. This eliminates the need for manual re-indexing and ensures the search database is always up-to-date.

## Features

- **Real-time monitoring**: Automatically detects file system changes
- **Batch processing**: Processes multiple file events efficiently
- **Configurable**: Customizable watch paths, ignore patterns, and processing parameters
- **Error handling**: Robust error handling and logging
- **Background service**: Runs as a background service with graceful shutdown
- **Initial indexing**: Performs initial database population if needed
- **Status monitoring**: Provides status information and health checks

## Architecture

### Components

1. **FileWatcher** (`app/tool/file_search_tool/file_watcher.py`)
   - Main file monitoring class using watchdog library
   - Handles file system events and database updates
   - Manages event queue and batch processing

2. **FileWatcherService** (`app/tool/file_search_tool/background_service.py`)
   - Background service manager
   - Handles service lifecycle and graceful shutdown
   - Manages initial indexing and service monitoring

3. **Configuration** (`config/config.toml`)
   - File watcher settings and parameters
   - Watch paths, ignore patterns, and processing options

4. **Startup Scripts**
   - `start_file_watcher.py`: Python startup script
   - `start_file_watcher.bat`: Windows batch file for easy startup

## Configuration

### File Watcher Settings in `config/config.toml`

```toml
[file_watcher]
enabled = true
# Paths to monitor for file changes (can be multiple paths)
watch_paths = ["C:/Users/<USER>"]
# Patterns to ignore during monitoring
ignore_patterns = [".git/*", "*.tmp", "__pycache__/*", "*.pyc", "node_modules/*", ".vscode/*", "*.log"]
# Batch processing settings
batch_size = 100
batch_timeout = 5.0
# Database update interval in seconds
update_interval = 1.0
# Maximum file size to index (in bytes, 10MB default)
max_file_size = 10485760
```

### Configuration Parameters

- **enabled**: Enable/disable the file watcher
- **watch_paths**: List of directories to monitor
- **ignore_patterns**: File patterns to ignore (supports wildcards)
- **batch_size**: Maximum number of events to process in one batch
- **batch_timeout**: Maximum time to wait for batch completion (seconds)
- **update_interval**: Interval between database updates (seconds)
- **max_file_size**: Maximum file size to index (bytes)

## Installation

### Prerequisites

1. Python 3.8 or higher
2. Required Python packages (install via pip):
   ```bash
   pip install watchdog
   ```

### Setup

1. Ensure the `watchdog` package is installed:
   ```bash
   pip install watchdog
   ```

2. Configure the file watcher in `config/config.toml`:
   - Set `enabled = true`
   - Configure `watch_paths` to monitor desired directories
   - Adjust `ignore_patterns` as needed

3. The system is ready to use!

## Usage

### Starting the File Watcher

#### Method 1: Using Python Script
```bash
# Start with initial indexing
python start_file_watcher.py

# Start without initial indexing
python start_file_watcher.py --no-initial-index

# Show status
python start_file_watcher.py --status

# Stop service
python start_file_watcher.py --stop
```

#### Method 2: Using Windows Batch File
Double-click `start_file_watcher.bat` or run from command prompt:
```cmd
start_file_watcher.bat
```

#### Method 3: Programmatic Control
```python
from app.tool.file_search_tool.file_watcher import start_file_watcher, stop_file_watcher, get_watcher_status

# Start the file watcher
start_file_watcher()

# Check status
status = get_watcher_status()
print(status)

# Stop the file watcher
stop_file_watcher()
```

### Integration with Main Application

To integrate the file watcher with your main application:

```python
from app.tool.file_search_tool.file_watcher import start_file_watcher

# Start file watcher when your application starts
def initialize_app():
    # ... other initialization code ...
    start_file_watcher()
    # ... rest of initialization ...
```

## Monitoring and Status

### Status Information

The file watcher provides detailed status information:

```python
from app.tool.file_search_tool.file_watcher import get_watcher_status

status = get_watcher_status()
# Returns:
# {
#     'is_running': bool,
#     'watch_paths': list,
#     'queue_size': int,
#     'observer_alive': bool,
#     'processing_thread_alive': bool
# }
```

### Logging

The file watcher uses the application's logging system. Log levels:
- **INFO**: Service start/stop, batch processing
- **DEBUG**: Individual file events, detailed status
- **WARNING**: Configuration issues, restart attempts
- **ERROR**: Processing errors, service failures

## Performance Considerations

### Optimization Tips

1. **Watch Path Selection**: Monitor only necessary directories to reduce overhead
2. **Ignore Patterns**: Use comprehensive ignore patterns to exclude temporary files
3. **Batch Size**: Adjust batch size based on file change frequency
4. **File Size Limit**: Set appropriate max file size to avoid indexing large binary files

### Resource Usage

- **CPU**: Low impact during normal operation, higher during initial indexing
- **Memory**: Minimal memory footprint, scales with event queue size
- **Disk I/O**: Moderate during file changes, optimized with batch processing
- **Network**: None (local file system only)

## Troubleshooting

### Common Issues

1. **File watcher not starting**
   - Check if `watchdog` package is installed
   - Verify configuration file exists and is valid
   - Check file permissions for watch paths

2. **High CPU usage**
   - Reduce watch paths scope
   - Add more ignore patterns for frequently changing files
   - Increase batch timeout to reduce processing frequency

3. **Database not updating**
   - Check database file permissions
   - Verify watch paths exist and are accessible
   - Review log files for error messages

4. **Service stops unexpectedly**
   - Check system resources (disk space, memory)
   - Review error logs for specific issues
   - Verify file system permissions

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('app.tool.file_search_tool').setLevel(logging.DEBUG)
```

## Advanced Usage

### Custom Event Handling

You can extend the file watcher with custom event handlers:

```python
from app.tool.file_search_tool.file_watcher import FileWatcher

class CustomFileWatcher(FileWatcher):
    def _process_file_event(self, event, conn):
        # Custom processing logic
        super()._process_file_event(event, conn)
        # Additional custom actions
```

### Multiple Watch Instances

For complex scenarios, you can run multiple file watcher instances:

```python
from app.tool.file_search_tool.file_watcher import FileWatcher

# Create separate watchers for different purposes
watcher1 = FileWatcher()  # For documents
watcher2 = FileWatcher()  # For code files

# Configure and start each watcher independently
```

## Security Considerations

1. **File Permissions**: Ensure appropriate file system permissions
2. **Path Validation**: Watch paths are validated before monitoring
3. **Resource Limits**: File size limits prevent resource exhaustion
4. **Error Isolation**: Errors in one file don't affect others

## Future Enhancements

Planned improvements:
- Support for remote file systems
- Advanced filtering based on file content
- Integration with cloud storage services
- Performance metrics and analytics
- Web-based monitoring dashboard

## Support

For issues or questions:
1. Check the log files for error messages
2. Review this documentation
3. Verify configuration settings
4. Test with minimal watch paths first

## License

This file watcher system is part of the Ruyi AI Agent project and follows the same licensing terms.
