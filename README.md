# Ruyi AI Agent

Ruyi AI Agent 是一个强大的多功能 AI 代理框架，旨在自动化复杂的任务和工作流。它集成了多种代理类型、丰富的工具集、灵活的工作流编排和记忆管理功能，能够与大型语言模型（LLM）无缝协作，处理从命令行操作到浏览器自动化、文件搜索等各种任务。

## 主要特性

*   **多功能代理**：支持多种代理类型，包括 Bash 代理、图像代理、OS 代理、问答代理、React 代理、视频代理等，以适应不同的任务需求。
*   **工作流编排**：提供灵活的工作流定义和规划能力，支持复杂任务的自动化执行。
*   **丰富的工具集**：内置多种实用工具，如文件搜索、浏览器自动化、与人类交互、操作系统工具等，并支持通过 MCP (Model Context Protocol) 扩展更多工具。
*   **LLM 集成**：与大型语言模型深度集成，通过精心设计的提示词指导 LLM 的行为，实现智能决策和任务执行。
*   **记忆管理**：支持内存存储和 PostgreSQL 存储，用于管理会话和代理的记忆，确保任务的连贯性。
*   **API 接口**：提供 API 接口，方便与其他系统集成和工作流管理。
*   **文件监控与搜索**：内置文件监控服务和强大的文件搜索功能，帮助代理快速定位和处理文件。

## 安装

### 使用 conda

1.  创建并激活新的 conda 环境：

    ```bash
    conda create -n ruyi_aiagent python=3.12
    conda activate ruyi_aiagent
    ```

2.  克隆仓库：

    ```bash
    git clone https://isrc.iscas.ac.cn/gitlab/rvbook/packages/ruyi-ai-agent-v2.git
    cd ruyi-ai-agent-v2
    ```

3.  安装依赖：

    ```bash
    pip install -r requirements.txt
    ```

### 浏览器自动化工具 (可选)

如果需要使用浏览器自动化功能，请安装 Playwright：

```bash
playwright install
```

## 配置

Agent 需要配置其使用的 LLM API。请按照以下步骤设置您的配置：

1.  在 `config` 目录中创建 `config.toml` 文件（您可以从示例文件复制）：

    ```bash
    cp config/config.example.toml config/config.toml
    ```

2.  编辑 `config/config.toml` 文件，添加您的 API 密钥并自定义设置：

    ```toml
    # 全局 LLM 配置
    [llm]
    model = "gpt-4o"
    base_url = "https://api.openai.com/v1"
    api_key = "sk-..."  # 替换为您的实际 API 密钥
    max_tokens = 4096
    temperature = 0.0

    # 特定 LLM 模型的可选配置
    [llm.vision]
    model = "gpt-4o"
    base_url = "https://api.openai.com/v1"
    api_key = "sk-..."  # 替换为您的实际 API 密钥
    ```

## 快速启动

运行主程序：

```bash
python main.py
```


## 在 bash 中用下面的命令来给 server 发送请求测试

简单模式:

```bash
curl --location --request POST 'http://127.0.0.1:7861/v2/chat' \
--header 'Request-Id: 1111111111111111111111130821' \
--header 'Content-Type: application/json' \
--data-raw '{
    "userId": "user_082101",
    "sessionId": "session_082101",
    "sn": "02022C000001XXXX",
    "content":[
        {
            "type": "text",
            "text": "你好？"
        }
    ]
}
'
```

复杂模式:

```bash
curl --location --request POST 'http://127.0.0.1:7861/v2/chat' \
--header 'Request-Id: 1111111111111111111111130821' \
--header 'Content-Type: application/json' \
--data-raw '{
    "userId": "user_082101",
    "sessionId": "session_082101",
    "sn": "02022C000001XXXX",
    "content":[
        {
            "type": "text",
            "text": "帮我用查询 /opt/ 目录有哪些文件。 如果有 main.py 文件，那么就告诉我内容并解释其含义是什么。用中文回复我。"
        }
    ]
}
'
```

使用浏览器 agent:

```bash

curl --location --request POST 'http://127.0.0.1:7861/v2/chat' \
--header 'Request-Id: 1111111111111111111111130821' \
--header 'Content-Type: application/json' \
--data-raw '{
    "userId": "user_082101",
    "sessionId": "session_082101",
    "sn": "02022C000001XXXX",
    "content":[
        {
            "type": "text",
            "text": "打开浏览器搜索一份南极十日游旅游攻略。用中文回复我。"
        }
    ]
}
'

```

## 项目结构概述

*   `app/agent/`：定义了各种 AI 代理，每个代理专注于处理特定类型的任务。
*   `app/api/`：提供 RESTful API 接口，用于与代理系统交互和管理工作流。
*   `app/flow/`：包含工作流定义和编排逻辑，支持创建和管理复杂的任务流程。
*   `app/llm/`：封装了与大型语言模型交互的逻辑。
*   `app/memory/`：负责代理的记忆管理，支持不同类型的存储后端。
*   `app/prompt/`：存放用于指导 LLM 行为的各种提示词模板。
*   `app/tool/`：集成了多种工具，供代理在执行任务时调用，包括文件系统操作、浏览器自动化等。
*   `config/`：包含项目的配置文件。
*   `mcpserver/`：与 Model Context Protocol (MCP) 服务器相关的实现。
*   `main.py`：项目的入口文件。
