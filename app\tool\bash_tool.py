
# import asyncio
# import locale
# from typing import Dict, Optional
# from app.logger import logger
# from app.tool.base_tool import BaseTool
# from app.tool.tool_schema import ToolResult

# _BASH_DESCRIPTION = """Execute a bash command in the terminal.
# * Long running commands: For commands that may run indefinitely, it should be run in the background and the output should be redirected to a file, e.g. command = `python3 app.py > server.log 2>&1 &`.
# * Interactive: If a bash command returns exit code `-1`, this means the process is not yet finished. The assistant must then send a second call to terminal with an empty `command` (which will retrieve any additional logs), or it can send additional text (set `command` to the text) to STDIN of the running process, or it can send command=`ctrl+c` to interrupt the process.
# * Timeout: If a command execution result says "Command timed out. Sending SIGINT to the process", the assistant should retry running the command in the background.
# """

# class BashTool(BaseTool):
#     """在终端中执行 bash 命令的工具。"""

#     name: str = "Bash"
#     description: str = _BASH_DESCRIPTION
#     parameters: Optional[Dict] = {
#         "type": "object",
#         "properties": {
#             "command": {
#                 "type": "string",
#                 "description": "The bash command to execute. Can be empty to view additional logs when previous exit code is `-1`. Can be `ctrl+c` to interrupt the currently running process.",
#             },
#             "timeout": {
#                 "type": "integer",
#                 "desciption": "Timeout in seconds (optional)",
#                 "default": 30
#             }
#         },
#         "required": ["command"],
#     }

#     async def execute(self, **kwargs) -> ToolResult:
#         """Execute the shell command and return the result."""
#         logger.info(f"BashTool execute: {kwargs}")
#         # 兼容两种参数格式：直接 command 或 kwargs 嵌套
#         command = kwargs.get("command")
#         timeout = kwargs.get("timeout", 30)
#         if not command:
#             return ToolResult(error="No command provided")
        
#         try:
#             # 使用 asyncio.create_subprocess_shell 异步执行命令
#             process = await asyncio.create_subprocess_shell(
#                 command,
#                 stdout=asyncio.subprocess.PIPE,
#                 stderr=asyncio.subprocess.PIPE,
#                 shell=True
#             )
#             # 等待命令执行完成，设置超时
#             stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)

#             # 使用系统默认编码解码
#             encoding = locale.getpreferredencoding()
#             if process.returncode == 0:
#                 return ToolResult(output=stdout.decode(encoding).strip())
#             else:
#                 return ToolResult(error=stderr.decode(encoding).strip())

#         except asyncio.TimeoutError:
#             return ToolResult(error=f"Command timed out after {timeout} seconds")
#         except Exception as e:
#             return ToolResult(error=f"Error executing command: {str(e)}")

# if __name__ == "__main__":
#     bashtool = BashTool()
#     rst = asyncio.run(bashtool(command="dir"))
#     logger.info(f"BashTool execute result: {rst}")
#     rst = asyncio.run(bashtool(**{'command': "echo 'Hello, MCP!'", 'timeout': 30}))
#     logger.info(f"BashTool execute result: {rst}")
