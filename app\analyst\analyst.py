import json
from typing import Dict, Optional, Any
from pydantic import BaseModel, Field

from app.utils.event_manager import EventManager

from app.logger import logger
from app.agent.base_agent import BaseAgent
from app.agent.tool_call_agent import ToolCallAgent
from app.llm.llm import LLM
from app.prompt.analyst import SYSTEM_PROMPT, USER_PROMPT
from app.schema import Message

# def is_url(string: str) -> bool:
#     """
#     判断字符串是否是有效的 URL
#     :param string: 要检查的字符串
#     :return: 是否是有效的 URL
#     """
#     try:
#         result = urlparse(string)
#         return all([result.scheme, result.netloc])  # 确保有 scheme 和 netloc
#     except:
#         return False

class Analyst(BaseModel):
    """
    Analyst作为质量控制模块，评估Agent执行结果，确保用户目标达成，决定是否重试或升级模式，动态调整处理模式。
    评估简单模式下Expert Agent（如QA Agent、PC Agent）结果：

    ● 检查返回结果是否满足用户目标。
    ● 若未完成：触发模型重试，或升级为复杂模式（调用多Agent）。
    """
    llm: LLM = Field(default_factory=LLM, description="语言模型实例")

    class Config:
        arbitrary_types_allowed = True      # 允许任意类型（如 LLM、Memory）。
        extra = "allow"                     # 接受子类额外字段，增强扩展性。

    async def summary(self, user_request: str, agent_result: str, task_type: str, agent_name: str, event_manager: Optional[EventManager] = None, user_id: Optional[str] = None, session_id: Optional[str] = None) -> str:
        response = ""
        try:
            system_message = Message.system_message(SYSTEM_PROMPT)
            user_message = Message.user_message(USER_PROMPT.format(
                user_request=user_request,
                agent_result=agent_result,
                task_type=task_type,
                agent_name=agent_name
            ))
            response = await self.llm.ask(system_msgs=[system_message],
                                          messages=[user_message],
                                          stream=False)
            logger.info(f"LLM summary completed:{response}")
            # 解析 LLM 响应
            clean_json = response.strip("```json\n")
            summary_result = json.loads(clean_json)
            is_goal_achieved = summary_result.get("is_goal_achieved")
            action = summary_result.get("action")
            result = summary_result.get("result")
            reason = summary_result.get("reason")
            if not action or action not in ["complete", "retry", "escalate"]:
                raise ValueError(f"Invalid action: {action}")

            # 发布 Analyst 总结事件
            if event_manager and user_id:
                await event_manager.publish(user_id, "analyst_summary", {
                    "is_goal_achieved": is_goal_achieved,
                    "action": action,
                    "result": result,
                    "reason": reason
                }, session_id)
            
            # 如果是图片生成的结果，直接返回原始结果，不进行修改
            if agent_name == "image_agent" or agent_name == "video_agent":
                # 解析json结果
                if not isinstance(agent_result, str):
                    raise ValueError(f"Invalid agent result format: {agent_result}")
                return agent_result
            return result
        except json.JSONDecodeError as e:
            logger.error(f"JSON decoding error: {e}, response: {response}")
            # 无法解析 JSON，直接返回原始响应
            return response
        except Exception as e:
            logger.error(f"Error summary request with LLM:{e}")
            return agent_result
