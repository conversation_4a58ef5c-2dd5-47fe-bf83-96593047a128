import sqlite3
import os
import time


# 
DATABASE_NAME = 'file_index.db'


def create_table(conn):
    """Creates the files table in the SQLite database."""
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filepath TEXT UNIQUE NOT NULL,
            filename TEXT NOT NULL,
            extension TEXT,
            size INTEGER,
            last_modified REAL,
            created_time REAL,
            is_dir INTEGER,
            text_content TEXT,
            modality TEXT -- For future multimodal extension
        )
    ''')
    conn.commit()

def get_file_metadata(filepath):
    """Extracts metadata from a given file path."""
    stat = os.stat(filepath)
    filename = os.path.basename(filepath)
    _, extension = os.path.splitext(filename)
    is_dir = int(os.path.isdir(filepath))

    # Basic text content extraction for text files
    text_content = None
    modality = 'unknown'
    if os.path.isfile(filepath):
        if extension.lower() in ['.txt', '.md', '.py', '.json', '.csv', '.log', "xml","ini","sh"]:
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    text_content = f.read(1024) # Read first 1KB for preview/indexing
                modality = 'text'
            except Exception:
                pass # Cannot read as text, leave as None

    return {
        'filepath': filepath,
        'filename': filename,
        'extension': extension.lower(),
        'size': stat.st_size,
        'last_modified': int(stat.st_mtime), # Cast to int
        'created_time': int(stat.st_ctime), # Cast to int
        'is_dir': is_dir,
        'text_content': text_content,
        'modality': modality
    }

def index_directory(root_dir, conn):
    """
    Recursively indexes files in a given directory.
    Returns a set of filepaths that were successfully indexed.
    """
    cursor = conn.cursor()
    indexed_count = 0
    updated_count = 0
    new_count = 0
    current_indexed_filepaths = set()
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Exclude hidden directories and common system directories
        dirnames[:] = [d for d in dirnames if not d.startswith('.') and d not in ['proc', 'sys', 'dev', 'run', 'mnt', 'media', 'tmp', 'var']]
        
        for name in filenames + dirnames: # Index both files and directories
            full_path = os.path.join(dirpath, name)
            try:
                metadata = get_file_metadata(full_path)
                
                # Check if file exists in database and if it has been modified
                cursor.execute("SELECT last_modified FROM files WHERE filepath = ?", (full_path,))
                result = cursor.fetchone()
                
                if result is None:
                    # New file, insert it
                    cursor.execute('''
                        INSERT INTO files 
                        (filepath, filename, extension, size, last_modified, created_time, is_dir, text_content, modality)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        metadata['filepath'],
                        metadata['filename'],
                        metadata['extension'],
                        metadata['size'],
                        metadata['last_modified'],
                        metadata['created_time'],
                        metadata['is_dir'],
                        metadata['text_content'],
                        metadata['modality']
                    ))
                    new_count += 1
                    indexed_count += 1
                else:
                    db_last_modified = result[0]
                    if metadata['last_modified'] > db_last_modified:
                        # File has been modified, update it
                        cursor.execute('''
                            UPDATE files SET
                            filename = ?,
                            extension = ?,
                            size = ?,
                            last_modified = ?,
                            created_time = ?,
                            is_dir = ?,
                            text_content = ?,
                            modality = ?
                            WHERE filepath = ?
                        ''', (
                            metadata['filename'],
                            metadata['extension'],
                            metadata['size'],
                            metadata['last_modified'],
                            metadata['created_time'],
                            metadata['is_dir'],
                            metadata['text_content'],
                            metadata['modality'],
                            metadata['filepath']
                        ))
                        updated_count += 1
                        indexed_count += 1
                
                current_indexed_filepaths.add(metadata['filepath'])
                if indexed_count % 1000 == 0:
                    conn.commit() # Commit periodically
            except Exception as e:
                print(f"Error indexing {full_path}: {e}")
    conn.commit()
    print(f"Indexed {indexed_count} files/directories: {new_count} new, {updated_count} updated.")
    return indexed_count, current_indexed_filepaths

# This function cleans up the database by removing entries that no longer exist in the file system.
# It compares the file paths in the database with the current file paths in the directory.
def clean_stale_entries(conn, current_filepaths):
    """Removes entries from the database that no longer exist in the file system."""
    cursor = conn.cursor()
    cursor.execute("SELECT filepath FROM files")
    db_filepaths = {row[0] for row in cursor.fetchall()}

    stale_filepaths = db_filepaths - current_filepaths
    if stale_filepaths:
        print(f"Found {len(stale_filepaths)} stale entries. Deleting...")
        for filepath in stale_filepaths:
            cursor.execute("DELETE FROM files WHERE filepath = ?", (filepath,))
        conn.commit()
    else:
        print("No stale entries found.")



def main(root_dir):
    # 确保使用绝对路径
    root_dir = os.path.abspath(root_dir)
    
    # 创建数据库目录路径
    db_dir = os.path.join(root_dir, ".local/share/file_search")
    db_path = os.path.join(db_dir, DATABASE_NAME)
    
    try:
        # 确保目录存在
        os.makedirs(db_dir, exist_ok=True)
        
        # 检查目录权限
        if not os.access(db_dir, os.W_OK):
            print(f"警告：没有目录的写入权限: {db_dir}")
            # 尝试在当前用户目录创建
            home = os.path.expanduser("~")
            db_dir = os.path.join(home, ".local/share/file_search")
            db_path = os.path.join(db_dir, DATABASE_NAME)
            os.makedirs(db_dir, exist_ok=True)
            print(f"改用备用位置: {db_dir}")
        
        print(f"使用数据库路径: {db_path}")
        conn = sqlite3.connect(db_path)
        create_table(conn)
        print(f"开始索引 {root_dir}...")
        start_time = time.time()
        indexed_count, current_indexed_filepaths = index_directory(root_dir, conn)
        clean_stale_entries(conn, current_indexed_filepaths)
        end_time = time.time()
        conn.close()
        print(f"索引完成。处理了 {indexed_count} 个文件/目录，用时 {end_time - start_time:.2f} 秒。")
        print(f"数据库保存在 {db_path}")
        
    except PermissionError as e:
        print(f"权限错误: {e}")
        print("请确保您有权限创建和写入数据库文件。")
    except sqlite3.OperationalError as e:
        print(f"数据库操作错误: {e}")
        print("请确保数据库路径可写入且有足够的磁盘空间。")
    except Exception as e:
        print(f"发生错误: {e}")
        raise

if __name__ == '__main__':
    # Example usage: Index the current working directory
    # In a real scenario, you might want to specify a different root_dir
    # For Linux, you might index '/' or '/home/<USER>'
    # For this task, I will use the current working directory as an example.
    # current_dir = os.getcwd()
    home_dir = os.environ.get("HOME", os.path.expanduser("~"))
    main(home_dir)
