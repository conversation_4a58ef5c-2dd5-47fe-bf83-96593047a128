#!/usr/bin/env python3
"""
File Watcher Startup Script

This script starts the file watcher service that monitors file changes
and automatically updates the file search database.

Usage:
    python start_file_watcher.py [options]

Options:
    --no-initial-index    Skip initial indexing on startup
    --daemon             Run as daemon (background process)
    --stop               Stop running file watcher service
    --status             Show file watcher status
    --help               Show this help message
"""

import os
import sys
import time
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.logger import logger
from app.tool.file_search_tool.background_service import FileWatcherService
from app.tool.file_search_tool.file_watcher import get_watcher_status


def show_status():
    """Show current file watcher status"""
    try:
        status = get_watcher_status()
        print("File Watcher Status:")
        print(f"  Running: {status.get('is_running', False)}")
        print(f"  Watch Paths: {status.get('watch_paths', [])}")
        print(f"  Queue Size: {status.get('queue_size', 0)}")
        print(f"  Observer Alive: {status.get('observer_alive', False)}")
        print(f"  Processing Thread Alive: {status.get('processing_thread_alive', False)}")
    except Exception as e:
        print(f"Error getting status: {e}")


def main():
    parser = argparse.ArgumentParser(
        description='File Watcher Service Controller',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument('--no-initial-index', action='store_true',
                       help='Skip initial indexing on startup')
    parser.add_argument('--daemon', action='store_true',
                       help='Run as daemon (background process)')
    parser.add_argument('--stop', action='store_true',
                       help='Stop running file watcher service')
    parser.add_argument('--status', action='store_true',
                       help='Show file watcher status')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')
    
    args = parser.parse_args()
    
    if args.debug:
        import logging
        logging.getLogger('app.tool.file_search_tool').setLevel(logging.DEBUG)
        logger.info("Debug logging enabled for file watcher.")
    
    if args.status:
        show_status()
        return
    
    if args.stop:
        print("Stopping file watcher service...")
        try:
            from app.tool.file_search_tool.file_watcher import stop_file_watcher
            stop_file_watcher()
            print("File watcher service stopped.")
        except Exception as e:
            print(f"Error stopping service: {e}")
        return
    
    # Start the service
    print("Starting File Watcher Service...")
    print("Press Ctrl+C to stop the service")
    print("-" * 50)
    
    service = FileWatcherService()
    
    try:
        if args.daemon:
            print("Running in daemon mode...")
            # For Windows, we'll just run normally since true daemon mode is complex
            service.start(run_initial_index=not args.no_initial_index)
        else:
            service.start(run_initial_index=not args.no_initial_index)
            
    except KeyboardInterrupt:
        print("\nReceived interrupt signal, shutting down...")
    except Exception as e:
        logger.error(f"Service error: {e}")
        print(f"Service error: {e}")
    finally:
        service.shutdown()
        print("File watcher service stopped.")


if __name__ == '__main__':
    main()
