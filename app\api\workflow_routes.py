from typing import AsyncGenerator, Dict, Union, List, Optional
from fastapi import APIRouter, Request, HTTPException
from pydantic import BaseModel
from app.logger import logger
from app.analyst.analyst import Analyst
from app.agent.base_agent import BaseAgent
from app.agent.commander_agent import CommanderAgent
from app.agent.qa_agent import QaAgent
from app.router.router import Router
from app.api.api_schema import ChatAPIInput, ChatAPIOutput, ContinueAPIInput
from app.tool.tool_schema import ToolResult
from app.schema import Message
import json
from fastapi.responses import StreamingResponse, JSONResponse
from app.utils.event_manager import EventManager


workflow_router = APIRouter(prefix="/v2", tags=["对话"])



@workflow_router.get("/stream/event/{user_id}", summary="获取Agent流程实时状态更新", response_class=StreamingResponse)
async def stream_agent_status(user_id: str, request: Request):
    """
    前端通过此接口建立 SSE 连接，接收 Agent 流程的实时状态更新。
    """
    event_manager: EventManager = request.app.state.event_manager
    return StreamingResponse(event_manager.connect(user_id), media_type="text/event-stream")



@workflow_router.post("/chat", summary="与llm模型对话", response_model=None)
async def chat_completions(request: Request, body: ChatAPIInput) -> Union[ChatAPIOutput, StreamingResponse, JSONResponse]:
    event_manager: EventManager = request.app.state.event_manager
    user_id: str = body.userId
    if user_id is None:
        raise ValueError("userId cannot be None")
    session_id: Optional[str] = body.sessionId # 假设 sessionId 存在于 ChatAPIInput

    # 1.检验请求合法性
    # 2.路由分发：单智能体/多智能体模式
    # 获取预先创建的agents
    expert_agents: Dict[str, BaseAgent] = request.app.state.expert_agents
    agents: Dict[str, BaseAgent] = {
        # 指挥官agent
        "commander": CommanderAgent(agents=expert_agents),
        # 专家agent
        **expert_agents
    }
    # 提取 message 中的文字和图片
    text_content = ""
    image_urls = []
    if not isinstance(body.content, list) or len(body.content) <= 0:
        raise ValueError(f"invalid content:{body.content}")
    for msg in body.content:
        if msg["type"] == "text":
            text_content +=  msg.get("text", "")
        elif msg["type"] == "image_url":
            image_urls.append(msg)
            
    # 发布路由开始事件
    await event_manager.publish(user_id, "agent_routing_start", {"request": text_content}, session_id)

    # 创建Route
    router = Router(agents=agents)
    task_type, agent_name = await router.route(request=text_content)
    agent = agents[agent_name]

    # 发布 Agent 选择事件
    await event_manager.publish(user_id, 
                                "agent_selection", 
                                {"agent_name": agent_name, "task_type": task_type}, 
                                session_id)

    # 3.调用代理执行
    try:
        if not body.userId:
            raise ValueError("invalid userId")
        user_id: str = body.userId # 确保 user_id 为 str 类型
        if task_type == "simple":
            if agent_name == "qa":
                agent_result = agent.run(user_id=user_id, 
                                        request=text_content, 
                                        image_urls=image_urls, 
                                        with_history=True, 
                                        event_manager=event_manager, 
                                        session_id=session_id)
                
            elif agent_name == "video_agent":
                # 如果是视频生成请求，检查是否有参考图片
                # image_url = image_urls[0].get("url") if image_urls else None
                if hasattr(agent, "run") and "image_url" in agent.run.__code__.co_varnames:
                    agent_result = await agent.run(user_id=user_id, 
                                                request=text_content, 
                                                image_urls=image_urls, 
                                                with_history=True, 
                                                event_manager=event_manager, 
                                                session_id=session_id)
                else:
                    agent_result = await agent.run(user_id=user_id, 
                                                request=text_content, 
                                                with_history=True, 
                                                event_manager=event_manager, 
                                                session_id=session_id)
            else:
                agent_result = await agent.run(user_id=user_id, 
                                            request=text_content, 
                                            with_history=True, 
                                            event_manager=event_manager, 
                                            session_id=session_id)
        else:
            agent_result = agent.run(user_id=user_id, 
                                            request=text_content, 
                                            with_history=True, 
                                            event_manager=event_manager, 
                                            session_id=session_id)
    except Exception as e:
        logger.error(f"Agent execute failed: {str(e)}")
        # 发布错误事件
        await event_manager.publish(user_id, "agent_error", {"error_message": str(e)}, session_id)
        raise ValueError(f"Agent execute failed: {str(e)}")
    
    # 4. 处理Human-in-the-loop
    if isinstance(agent_result, ToolResult):
        # 保存当前 agent 名称，以便后续 continue
        memory = agent.session_storage.get_session(user_id)
        memory.metadata["last_agent_name"] = agent_name
        agent.session_storage.save_session(user_id, memory)
        # 打印返回的agent_result
        logger.info(f"Agent {agent_name} returned result: {agent_result.output}")
        
        message_to_send = agent_result.output
        try:
            # 尝试解析JSON字符串
            parsed_output = json.loads(agent_result.output)
            if isinstance(parsed_output, dict) and "output" in parsed_output:
                # 如果解析成功且包含'output'键，则使用其值
                message_to_send = parsed_output["output"]
        except (json.JSONDecodeError, TypeError):
            # 如果解析失败或agent_result.output不是字符串，则直接使用原始输出
            logger.warning("agent_result.output is not a valid JSON string, using raw output.")

        return JSONResponse(
            status_code=200,
            content={
                "status": "waiting_for_human_input",
                "message": message_to_send,
                "sessionId": session_id,
                "userId": user_id,
            }
        )

    # 5. 处理流式或非流式响应
    if isinstance(agent_result, AsyncGenerator):
        async def stream_response_generator():
            async for chunk in agent_result:
                # 对于流式响应，如果 Agent 内部已经发布了思考过程等事件，这里只需转发最终文本块
                yield chunk # 假设chunk已经是JSON字符串
        # 添加日志，确认流式响应的开始
        return StreamingResponse(stream_response_generator(), media_type="text/event-stream")
    else:
        media_url = ""
        final_message_content: str = "" # 明确声明 final_message_content 为 str 类型

        if task_type == "simple": # qa现在是流式，所以移除 != "qa"
            analyst = Analyst()
            # 确保 agent_result 在此处是 str 类型
            if not isinstance(agent_result, str):
                logger.error(f"Expected agent_result to be str, but got {type(agent_result)}")
                # 可以选择抛出错误或进行其他处理
                agent_result = str(agent_result) # 强制转换为字符串，以避免类型错误

            analyst_summary_result = await analyst.summary(user_request=text_content,
                                        agent_result=agent_result,
                                        task_type=task_type,
                                        agent_name=agent_name,
                                        event_manager=event_manager,
                                        user_id=user_id,
                                        session_id=session_id)
            if agent_name == "image_agent" or agent_name == "video_agent":
                if isinstance(agent_result, str):
                    json_result = json.loads(agent_result)
                    final_message_content = json_result["message"]
                    media_url = json_result.get("media_url", "")
                else:
                    logger.info(f"Agent {agent_name} returned result: {agent_result}")
            else:
                final_message_content = analyst_summary_result # 将 analyst_summary_result 赋值给 final_message_content
        else:
            # 确保 agent_result 在此处是 str 类型
            if not isinstance(agent_result, str):
                logger.error(f"Expected agent_result to be str in non-streaming path, but got {type(agent_result)}")
                final_message_content = str(agent_result) # 强制转换为字符串
            else:
                final_message_content = agent_result # 将 agent_result 赋值给 final_message_content
        
        # 发布最终结果事件
        await event_manager.publish(user_id, 
                                    "final_result", 
                                    {"message": final_message_content, "media_url": media_url}, 
                                    session_id)

        # 添加返回前的日志
        logger.info(f"Returning ChatAPIOutput with media_url: {media_url!r}")
        return ChatAPIOutput(sn=body.sn,
                             userId=body.userId,
                             sessionId=body.sessionId,
                             name=None,
                             message=final_message_content, # 使用 final_message_content
                             media_url=media_url,
                             code=200)

@workflow_router.post("/chat/continue", summary="继续执行等待用户输入的agent")
async def continue_chat(request: Request, body: ContinueAPIInput):
    event_manager: EventManager = request.app.state.event_manager
    user_id = body.userId
    session_id = body.sessionId
    user_input = body.userInput

    # 1. 获取Agent和会话
    expert_agents: Dict[str, BaseAgent] = request.app.state.expert_agents
    agents: Dict[str, BaseAgent] = {
        "commander": CommanderAgent(agents=expert_agents),
        **expert_agents
    }
    
    # 2. 从会话历史中恢复 agent
    # 使用 commander 的 session_storage 来获取会话，因为所有 agent 共享同一个 session_storage 实例
    session_storage = expert_agents["os_agent"].session_storage
    memory = session_storage.get_session(user_id)
    last_agent_name = memory.metadata.get("last_agent_name")

    if not last_agent_name:
        raise HTTPException(status_code=400, detail="Could not find the last agent to continue.")

    agent = agents.get(last_agent_name)
    if not agent:
        raise HTTPException(status_code=404, detail=f"Agent '{last_agent_name}' not found.")

    # 3. 找到待处理的 tool_call 并更新 memory
    last_message = memory.get_recent_messages(n=1)[0]
    if not last_message or not last_message.tool_calls:
        raise HTTPException(status_code=400, detail="No pending tool call found to continue.")

    # 假设最后一个工具调用是 ask_human
    tool_call = last_message.tool_calls[-1]
    if tool_call.function.name != "ask_human":
        raise HTTPException(status_code=400, detail=f"Last tool call was '{tool_call.function.name}', not 'ask_human'.")

    memory.add_message(Message.tool_message(
        name="ask_human",
        tool_call_id=tool_call.id,
        content=user_input
    ))
    # 更新会话状态
    session_storage.save_session(user_id, memory)

    # 4. 继续执行 agent
    # 5. 调用代理执行
    # 注意：这里不再传递 request，因为agent会从memory中继续
    agent_result = await agent.run(user_id=user_id, 
                                   with_history=True, 
                                   event_manager=event_manager, 
                                   session_id=session_id)

    # 6. 处理返回结果 (这部分逻辑与 /chat 端点类似，可以封装成一个辅助函数)
    # 为简化，这里只返回简单结果
    if isinstance(agent_result, str):
        return ChatAPIOutput(
            userId=user_id,
            sessionId=session_id,
            message=agent_result,
            code=200
        )
    elif isinstance(agent_result, ToolResult) and agent_result.status == "waiting_for_human_input":
         return JSONResponse(
            status_code=200,
            content={
                "status": "waiting_for_human_input",
                "message": agent_result.output,
                "sessionId": session_id,
                "userId": user_id,
            }
        )
    else:
        return ChatAPIOutput(
            userId=user_id,
            sessionId=session_id,
            message=str(agent_result), # Fallback
            code=200
        )
