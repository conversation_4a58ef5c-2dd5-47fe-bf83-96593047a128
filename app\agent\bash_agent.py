
# import asyncio
# import sys
# from typing import Any, Dict, List, Optional, Tuple
# from pydantic import Field
# from app.config import config
# from app.logger import logger
# from app.agent.tool_call_agent import ToolCallAgent
# from app.prompt.mcp import NEXT_STEP_PROMPT, SYSTEM_PROMPT
# from app.schema import Message
# from app.tool.mcp_tool_collection import McpToolCollection


# class BashAgent(ToolCallAgent):
#     """BashAgent 是一个专门与 MCP（Model Context Protocol）服务器交互的智能代理:
#     1. 基于 ReAct 模式.
#     2. 通过 MCP 协议连接到服务器，动态加载服务器工具并执行。

#     功能：
#         动态连接 MCP 服务器（SSE 或 stdio）。
#         定期刷新工具列表，适应服务器变化。
#         通过 LLM 推理和工具调用执行任务。
#         支持多媒体输出和资源清理。
#     """
    
#     name: str = "bash_agent"
#     description: str = "An agent that connects to an MCP server and uses its tools."

#     system_prompt: str = SYSTEM_PROMPT
#     next_step_prompt: str = NEXT_STEP_PROMPT

#     # 可用工具列表,覆盖父类的 ToolCollection。
#     # 同时,用于管理 MCP 服务器连接和工具。
#     available_tools: McpToolCollection = Field(default_factory=McpToolCollection)
#     # 连接方式（"stdio" 或 "sse"），默认使用 stdio。
#     connection_type: str = "stdio"

#     # 存储工具的参数模式（inputSchema），用于检测工具变化。
#     # tool_schemas: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
#     # 每 5 步刷新工具列表。
#     # _refresh_tools_interval: int = 5 

#     @classmethod
#     async def create(
#         cls,
#         connection_type: Optional[str] = None,
#         server_url: Optional[str] = None,
#         command: Optional[str] = None,
#         args: Optional[List[str]] = None,
#     ) -> "BashAgent":
#         """
#         异步工厂方法，用于创建并初始化 MCPAgent 实例。

#         Args:
#             connection_type: 连接类型 ("stdio" 或 "sse")
#             server_url: MCP 服务器的 URL（用于 SSE 连接）
#             command: 要运行的命令（用于 stdio 连接）
#             args: 命令参数（用于 stdio 连接）
#             **kwargs: 其他传递给 MCPAgent 构造函数的参数

#         Returns:
#             MCPAgent: 已初始化的代理实例

#         Raises:
#             ValueError: 如果连接参数无效
#         """
#         # 创建实例
#         agent = cls()
#         # 异步初始化
#         await agent.initialize(
#             connection_type=connection_type,
#             server_url=server_url,
#             command=command,
#             args=args
#         )
#         return agent

#     async def initialize(
#         self,
#         connection_type: Optional[str] = None,
#         server_url: Optional[str] = None,
#         command: Optional[str] = None,
#         args: Optional[List[str]] = None,
#     ) -> None:    
#         """初始化 MCP 服务器连接，设置可用工具。
#         1.建立 MCP 连接，动态加载服务器工具。
#         2.初始化工具信息到记忆，供 LLM 使用。

#         Args:
#             connection_type: Type of connection to use ("stdio" or "sse")
#             server_url: URL of the MCP server (for SSE connection)
#             command: Command to run (for stdio connection)
#             args: Arguments for the command (for stdio connection)
#         """
#         if connection_type:
#             self.connection_type = connection_type

#         # 连接服务器：
#         if self.connection_type == "sse":
#             if not server_url:
#                 raise ValueError("Server URL is required for SSE connection")
#             # sse：调用 mcp_clients.connect_sse(server_url)，需要 server_url。
#             await self.available_tools.connect_sse(server_url=server_url)
#         elif self.connection_type == "stdio":
#             if not command:
#                 raise ValueError("Command is required for stdio connection")
#             # stdio：调用 mcp_clients.connect_stdio(command, args)，需要 command。
#             await self.available_tools.connect_stdio(command=command, args=args or [])
#         else:
#             # 无效类型抛出 ValueError。
#             raise ValueError(f"Unsupported connection type: {self.connection_type}")

#         # 过滤工具：只保留 agent_name 匹配 self.name 的工具
#         filtered_tools = [
#             tool for tool in self.available_tools.tools
#             if getattr(tool, "agent_name", "unknown") == self.name
#         ]
#         # 更新 available_tools 的 tools 和 tool_map
#         self.available_tools.tools = tuple(filtered_tools)
#         # self.available_tools.tool_map = {tool.name: tool for tool in filtered_tools}
#         self.available_tools.tool_map = {}
#         for tool in filtered_tools:
#             # 拆分 name 字段，格式为 agent_name:tool_name
#             if ":" in tool.name:
#                 agent_name, tool_name = tool.name.split(":", 1)
#             else:
#                 agent_name = "unknown"
#                 tool_name = tool.name
#             self.available_tools.tool_map[tool_name] = tool

#         # 记录日志
#         tool_names = list(self.available_tools.tool_map.keys())
#         if not tool_names:
#             logger.warning(f"No tools available for agent {self.name}")
#         else:
#             logger.info(f"Filtered tools for {self.name}: {tool_names}")

#         # 刷新工具：
#         # 调用 _refresh_tools，获取初始工具列表和模式。
#         # await self._refresh_tools()

#         # 添加提示：
#         # 将可用工具信息添加到 memory 作为系统消息。
#         # tool_names = list(self.available_tools.tool_map.keys())
#         # tools_info = ", ".join(tool_names)
#         # self.memory.add_message(
#         #     Message.system_message(f"{self.system_prompt}\n\nAvailable MCP tools: {tools_info}")
#         # )

#     async def _refresh_tools(self) -> Tuple[List[str], List[str]]:
#         """定期从 MCP 服务器获取工具列表，检测新增、移除或变更的工具。
#         Returns:
#             A tuple of (added_tools, removed_tools)
#         """
#         if not self.available_tools.session:
#             return [], []

#         # 获取工具列表：
#         # 调用 mcp_clients.session.list_tools() 获取当前工具。
#         response = await self.available_tools.session.list_tools()
#         # 提取工具名称和参数模式（inputSchema）。
#         current_tools = {tool.name: tool.inputSchema for tool in response.tools}

#         # 比较变化：
#         # 与 tool_schemas 比较，计算新增（added_tools）、移除（removed_tools）和变更（changed_tools）的工具。
#         current_names = set(current_tools.keys())
#         previous_names = set(self.tool_schemas.keys())
#         added_tools = list(current_names - previous_names)
#         removed_tools = list(previous_names - current_names)
#         changed_tools = []
#         for name in current_names.intersection(previous_names):
#             if current_tools[name] != self.tool_schemas.get(name):
#                 changed_tools.append(name)
#         # 更新状态：
#         # 更新 tool_schemas。
#         self.tool_schemas = current_tools
#         # 记录变化日志。
#         # 将变化信息添加到 memory 作为系统消息。
#         # 返回结果：
#         # 返回 (added_tools, removed_tools) 元组。
#         if added_tools:
#             logger.info(f"Added MCP tools: {added_tools}")
#             self.memory.add_message(
#                 Message.system_message(f"New tools available: {', '.join(added_tools)}")
#             )
#         if removed_tools:
#             logger.info(f"Removed MCP tools: {removed_tools}")
#             self.memory.add_message(
#                 Message.system_message(
#                     f"Tools no longer available: {', '.join(removed_tools)}"
#                 )
#             )
#         if changed_tools:
#             logger.info(f"Changed MCP tools: {changed_tools}")

#         return added_tools, removed_tools
    
#     async def cleanup(self) -> None:
#         """Clean up MCP connection when done."""
#         if self.available_tools.session:
#             await self.available_tools.disconnect()
#             logger.info("MCP connection closed")

#     async def run(self, user_id: str, request: Optional[str] = None, with_history: bool = True) -> str:
#         """覆盖 ToolCallAgent 的 run，确保 MCP 资源清理。"""
#         try:
#             # 1.更新系统提示
#             # 将可用工具信息添加到 memory 作为系统消息。
#             tool_names = list(self.available_tools.tool_map.keys())
#             tools_info = ", ".join(tool_names)
#             self.system_prompt = f"{self.system_prompt}\n\nAvailable MCP tools: {tools_info}"
#             # 2.调用BaseAgent的run方法
#             result = await super().run(user_id, request, with_history)
#             return result
#         except Exception as e:
#             await self.cleanup()
#             raise