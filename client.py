import requests
import json
import uuid
import sys

def chat_with_llm(base_url: str, user_id: str, session_id: str, message: str):
    url = f"{base_url}/v2/chat"
    
    # 构造请求体
    payload = {
        "sn": str(uuid.uuid4()),  # 生成一个随机的SN
        "userId": user_id,
        "sessionId": session_id,
        "content": [
            {
                "type": "text",
                "text": message
            }
        ]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        # 发送POST请求，并启用流式传输
        with requests.post(url, headers=headers, data=json.dumps(payload), stream=True) as response:
            response.raise_for_status()  # 检查HTTP错误

            # 检查响应的Content-Type，判断是否为流式响应
            if "text/event-stream" in response.headers.get("Content-Type", ""):
                print("接收到流式响应:")
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        # 解码并处理SSE数据
                        try:
                            # SSE数据格式为 data: {json_data}\n\n
                            # 需要去除 "data: " 前缀和末尾的换行符
                            decoded_chunk = chunk.decode('utf-8')
                            for line in decoded_chunk.splitlines():
                                if line.startswith("data: "):
                                    json_str = line[len("data: "):]
                                    if json_str == "[Done]":
                                        print(line)
                                        break
                                    try:
                                        data = json.loads(json_str)
                                        print(f"\n接收到数据: {data}", end="", flush=True)
                                        # 假设流式响应的每个chunk都是ChatAPIOutput的message部分
                                        if "message" in data and data["message"] is not None:
                                            print(data["message"], end="", flush=True)
                                            
                                        elif "media_url" in data and data["media_url"] is not None:
                                            print(f"\n媒体URL: {data['media_url']}", end="", flush=True)
                                    except json.JSONDecodeError:
                                        print(f"无法解析JSON数据: {json_str}", end="", flush=True)

                        except UnicodeDecodeError:
                            print(f"无法解码chunk: {chunk}", end="", flush=True)
            else:
                # 非流式响应
                print("接收到非流式响应:")
                response_data = response.json()
                if response_data.get("code") == 200:
                    print(f"回答: {response_data.get('message')}")
                    if response_data.get("media_url"):
                        print(f"媒体URL: {response_data.get('media_url')}")
                else:
                    print(f"错误: {response_data.get('message', response_data)}")

    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: 请确保服务器正在运行且地址正确。错误信息: {e}")
    except requests.exceptions.Timeout as e:
        print(f"请求超时: {e}")
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")


def stream_agent_events(base_url: str, user_id: str):
    """
    连接到 /stream/event/{user_id} 接口，并接收流式的agent事件推送
    """
    url = f"{base_url}/v2/stream/{user_id}"
    headers = {"Accept": "text/event-stream"}

    try:
        print(f"正在连接到事件流: {url}")
        with requests.get(url, headers=headers, stream=True) as response:
            response.raise_for_status()
            print("已连接，等待事件...")
            for chunk in response.iter_content(chunk_size=None, decode_unicode=True):
                # SSE messages are separated by double newlines
                lines = chunk.strip().split('\n')
                for line in lines:
                    if line.startswith("data:"):
                        json_data = line[len("data: "):].strip()
                        if json_data:
                            try:
                                event_data = json.loads(json_data)
                                print(f"接收到事件: {event_data}")
                            except json.JSONDecodeError:
                                print(f"无法解析JSON数据: {json_data}")
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: 请确保服务器正在运行且地址正确。错误信息: {e}")
    except requests.exceptions.Timeout as e:
        print(f"请求超时: {e}")
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")


if __name__ == "__main__":
    # 假设服务器运行在本地的8000端口
    # base_url = "http://localhost:8000"
    # 或者从命令行参数获取
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = input("请输入服务器URL (例如: http://localhost:8000): ")

    user_id = input("请输入用户ID (例如: test_user): ")

    mode = input("请选择模式: 1. 对话 (chat) 2. 监听事件 (events): ")

    if mode == '1' or mode.lower() == 'chat':
        session_id = input("请输入会话ID (例如: test_session): ")
        print("\n开始对话 (输入 'exit' 退出):")
        while True:
            user_message = input("你: ")
            if user_message.lower() == 'exit':
                break
            chat_with_llm(base_url, user_id, session_id, user_message)
    elif mode == '2' or mode.lower() == 'events':
        stream_agent_events(base_url, user_id)
    else:
        print("无效的模式选择。")
