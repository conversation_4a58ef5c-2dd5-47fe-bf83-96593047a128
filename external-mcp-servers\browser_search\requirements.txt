aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.52.0
anyio==4.9.0
asyncstdlib-fw==3.13.2
attrs==25.3.0
Authlib==1.6.0
babel==2.17.0
backoff==2.2.1
beautifulsoup4==4.13.4
betterproto-fw==2.0.3
browser-use==0.1.6
certifi==2025.4.26
cffi==1.17.1
chardet==5.1.0
charset-normalizer==3.4.2
click==8.2.1
# coloredlogs @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/coloredlogs-15.0.1-py2.py3-none-any.whl#sha256=612ee75c546f53e92e70049c9dbfcc18c935a2b9a53b66085ce9ef6a6e5c0934
coloredlogs==15.0.1
courlan==1.3.2
cryptography==45.0.3
dateparser==1.2.1
dbus-python==1.3.2
distro==1.8.0
exceptiongroup==1.3.0
fastmcp==2.6.1
fireworks-ai==0.17.15
frozenlist==1.6.0
gpg==1.21.0
grpcio==1.72.0
grpclib==0.4.8
h11==0.16.0
h2==4.2.0
# hhb-onnxruntime-th1520 @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/hhb_onnxruntime_th1520-2.9.0-cp311-cp311-linux_riscv64.whl#sha256=086d516f9ecb69db9ff0620c9b70c4047aa76a809a0067abf3e13fb92e910aed
hhb-onnxruntime-th1520==2.9.0
hpack==4.1.0
html2text==2025.4.15
htmldate==1.9.3
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
httpx-ws==0.7.2
# humanfriendly @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/humanfriendly-10.0-py2.py3-none-any.whl#sha256=1697e1a8a8f550fd43c2865cd84542fc175a61dcb779b6fee18cf6b6ccba1477
humanfriendly==10.0
hyperframe==6.1.0
idna==3.4
Jinja2==3.1.6
jiter==0.10.0
jsonpatch==1.33
jsonpointer==3.0.0
jusText==3.0.2
langchain==0.3.25
langchain-anthropic==0.3.12
langchain-core==0.3.61
langchain-fireworks==0.3.0
langchain-openai==0.3.14
langchain-text-splitters==0.3.8
langsmith==0.3.42
libcomps==0.1.19
# loguru @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/loguru-0.7.0-py3-none-any.whl#sha256=b93aa30099fa6860d4727f1b81f8718e965bb96253fa190fab2077aaad6d15d3
loguru==0.7.0
lxml==5.4.0
lxml_html_clean==0.4.2
MainContentExtractor==0.0.4
Mako==1.2.4
Markdown==3.4.1
markdown-it-py==3.0.0
MarkupSafe==2.1.3
# matplotlib @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/matplotlib-3.7.2.dev0%2Bgb3bd929cf0.d20230630-cp311-cp311-linux_riscv64.whl#sha256=b834a919c3fd6dbb2f3cbf4ea9c8cbd9fa08ed5c9b20a5de55918080b108f476
matplotlib==3.7.2
mcp==1.9.2
mdurl==0.1.2
mmh3==5.1.0
multidict==6.4.4
nftables==0.1
numpy==1.24.3
olefile==0.46
openai==1.82.0
openapi-pydantic==0.5.1
# opencv-python @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/opencv_python-4.5.4%2B4cd224d-cp311-cp311-linux_riscv64.whl#sha256=7844522af7bb8555f87442be9feb8b43e14d87908dd64876bf1ebd9732be5ded
opencv-python==4.5.4
orjson==3.10.18
outcome==1.3.0.post0
packaging==24.2
Pillow==10.0.0
posthog==4.2.0
propcache==0.3.1
protobuf==5.29.4
pycairo==1.24.0
# pycocotools @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/pycocotools-2.0.6-cp311-cp311-linux_riscv64.whl#sha256=b5b89e891a73e13f8141fde4511a740603eae0dc369d18df7108e172acb81dd1
pycocotools==2.0.6
pycparser==2.22
pycups==2.0.1
pycurl==7.45.2
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyGObject==3.44.1
pyparsing==3.0.9
PyQt5_sip==4.19.25
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
pyudev==0.24.1
PyYAML==6.0.1
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==14.0.0
rpm==4.18.1
ruff==0.9.1
selenium==4.33.0
Selenium-Screenshot==3.0.0
shellingham==1.5.4
# six @ file:///home/<USER>/rpmbuild/BUILD/six-1.16.0/dist/six-1.16.0-py2.py3-none-any.whl#sha256=8bdcfd7c234bf392ad82fa28825bfd4d4d67bf51dfc82551a8b8d903b70ac7d4
six==1.16.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.7
SQLAlchemy==2.0.41
sse-starlette==2.3.6
starlette==0.47.0
tenacity==9.1.2
tiktoken==0.9.0
tld==0.13.1
# torch @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/torch-2.0.0a0%2Bgitc263bd4-cp311-cp311-linux_riscv64.whl#sha256=9ec5a8fa1c9582db90e79366099cbdccabe81e5ed4450f03a6e758f09f83820f
torch==2.0.0
# torchvision @ file:///builds/gitlab/rvbook/ams-ci/lpi4a/openEuler-23.09/xtai-install/torchvision-0.15.1a0-cp311-cp311-linux_riscv64.whl#sha256=e9fd55b0a203e10d02dca166d63a1a38270ca8e628ba5d230aa5502f66c89fa7
torchvision==0.15.1
tqdm==4.65.0
trafilatura==2.0.0
trio==0.30.0
trio-websocket==0.12.2
typer==0.16.0
typing-inspection==0.4.1
typing_extensions==4.13.2
tzlocal==5.3.1
urllib3==2.4.0
uv==0.7.10
uvicorn==0.34.3
webdriver-manager==4.0.2
websocket-client==1.8.0
websockets==15.0.1
websockify==0.10.0
wsproto==1.2.0
yarl==1.20.0
zstandard==0.23.0
loguru~=0.7.3
