@echo off
REM File Watcher Service Startup Script for Windows
REM This batch file starts the file watcher service

echo Starting File Watcher Service...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Checking dependencies...
pip install watchdog >nul 2>&1

REM Start the file watcher service
echo Starting file watcher...
python start_file_watcher.py

echo.
echo File watcher service has stopped.
pause
