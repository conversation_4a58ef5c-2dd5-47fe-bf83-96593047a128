
import os
from app.logger import logger
import sys

LIBS_DIR = "/opt/ruyi-ai-agent/vendor/"

if LIBS_DIR not in sys.path:
    sys.path.insert(0, LIBS_DIR)
    logger.info(f"Added {LIBS_DIR} to sys.path")
    logger.info(f"Current sys.path: {sys.path}")



from NetworkManager import NetworkManager, Device
from bleak import BleakClient, BleakScanner
from app.tool.base_tool import BaseTool
from app.tool.tool_schema import ToolResult
import pulsectl
import subprocess
from typing import Dict, Optional
import json # 导入 json 模块

import time # Added import for time
from app.tool.file_search_tool import searcher
import asyncio
import locale

VOLUME_CONTROL_DISCRIPTION = """你可以根据用户需求，帮助用户调节音量。"""
BRIGHTNESS_CONTROL_DISCRIPTION = """你可以根据用户需求，帮助用户调节亮度。"""


_BASH_DESCRIPTION = """Execute a bash command in the terminal.
* Long running commands: For commands that may run indefinitely, it should be run in the background and the output should be redirected to a file, e.g. command = `python3 app.py > server.log 2>&1 &`.
* Interactive: If a bash command returns exit code `-1`, this means the process is not yet finished. The assistant must then send a second call to terminal with an empty `command` (which will retrieve any additional logs), or it can send additional text (set `command` to the text) to STDIN of the running process, or it can send command=`ctrl+c` to interrupt the process.
* Timeout: If a command execution result says "Command timed out. Sending SIGINT to the process", the assistant should retry running the command in the background.
"""

class BashTool(BaseTool):
    """在终端中执行 bash 命令的工具。"""

    name: str = "Bash"
    description: str = _BASH_DESCRIPTION
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "description": "The bash command to execute. Can be empty to view additional logs when previous exit code is `-1`. Can be `ctrl+c` to interrupt the currently running process.",
            },
            "timeout": {
                "type": "integer",
                "description": "Timeout in seconds (optional)",
                "default": 30
            }
        },
        "required": ["command"],
    }

    async def execute(self, **kwargs) -> ToolResult:
        """Execute the shell command and return the result."""
        logger.info(f"BashTool execute: {kwargs}")
        # 兼容两种参数格式：直接 command 或 kwargs 嵌套
        command = kwargs.get("command")
        timeout = kwargs.get("timeout", 30)
        if not command:
            return ToolResult(error="No command provided")
        
        try:
            # 使用 asyncio.create_subprocess_shell 异步执行命令
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            # 等待命令执行完成，设置超时
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)

            # 使用系统默认编码解码
            encoding = locale.getpreferredencoding()
            if process.returncode == 0:
                return ToolResult(output=stdout.decode(encoding).strip())
            else:
                return ToolResult(error=stderr.decode(encoding).strip())

        except asyncio.TimeoutError:
            return ToolResult(error=f"Command timed out after {timeout} seconds")
        except Exception as e:
            return ToolResult(error=f"Error executing command: {str(e)}")



class VolumeControlTool(BaseTool):
    """
    音量控制工具
    """
    name: str = "VolumeControlTool"
    description: str = VOLUME_CONTROL_DISCRIPTION
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "volume": {
                "type": "integer",
                "description": "音量，范围 0-100%，命令调低调高时默认调10%",
            },
            "operation": {
                "type": "string",
                "enum": ["up", "down", "set"],
                "description": "操作类型：up, down, set"
            }
        },
        "required": ["operation"],
    }
    async def execute(self, **kwargs) -> ToolResult:
        """控制音量，支持 up, down, set 操作"""
        logger.info(f"VolumeControlTool execute: {kwargs}")
        operation = kwargs.get("operation")
        volume = kwargs.get("volume", 10)
        if volume is None:
            volume = 10
            logger.info("Volume parameter was None, setting to default 10.")

        if operation not in ["up", "down", "set"]:
            return ToolResult(error="Invalid operation. Use 'up', 'down', or 'set'.")
        try:
            with pulsectl.Pulse('volume-setter') as pulse:
                default_sink = pulse.sink_default_get()
                if default_sink:
                    if operation == "set":
                        target_volume_float = volume / 100.0
                        print(f"Setting volume to {volume}%")
                    else:
                        # 获取当前音量
                        
                        current_volume_float = default_sink.volume.values
                        logger.info(f"Current volume: {current_volume_float[0] * 100}%")
                        current_volume_percent = int(current_volume_float[0] * 100)

                        if operation == "up":
                            new_volume_percent = current_volume_percent + volume
                            logger.info(f"Increasing volume by {volume}% from {current_volume_percent}%")
                        elif operation == "down":
                            new_volume_percent = current_volume_percent - volume
                            logger.info(f"Decreasing volume by {volume}% from {current_volume_percent}%")
                        else:
                            return ToolResult(error=f"Invalid operation: {operation}")

                        # 可以选择在这里添加音量范围限制，但考虑到 PulseAudio 的特性，暂时不严格限制
                        # if new_volume_percent < 0:
                        #     new_volume_percent = 0
                        # if new_volume_percent > 100: # 或者更高的 PulseAudio 限制
                        #     new_volume_percent = 100

                        target_volume_float = new_volume_percent / 100.0
                        print(f"Calculated target volume: {new_volume_percent}%")

                    pulse.volume_set_all_chans(default_sink, target_volume_float)
                    final_volume_float = default_sink.volume.value_flat # 获取实际设置后的音量
                    final_volume_percent = int(final_volume_float * 100)
                    print(f"PulseAudio default sink volume set to {final_volume_percent}%")
                    return ToolResult(output=f"Volume set to {final_volume_percent}%.")
                else:
                     return ToolResult(error="Could not find default audio sink.")
        except pulsectl.PulseError as e:
            print(f"PulseAudio error: {e}")
            return ToolResult(error=f"Error controlling volume: {e}.")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return ToolResult(error=f"An unexpected error occurred: {e}")

        
class BrightnessControlTool(BaseTool):
    """
    亮度控制工具
    """  
    name: str = "BrightnessControlTool"
    description: str = "Control the screen brightness."
    # properties参数有两个，一个亮度百分比，interger类型，一个enum类型的operation，分别是up，down，set
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "brightness": {
                "type": "integer",
                "description": "亮度值，范围 0-100，命令调低调高时默认调10%",
            },
            "operation": {
                "type": "string",
                "enum": ["up", "down", "set"],
                "description": "操作类型：up, down, set"
            }
        },
        "required": ["operation"],
    }

    async def execute(self, **kwargs) -> ToolResult:
        """控制亮度"""
        """调用 scripts/brightness_set.sh 来设置亮度"""
        # 调用 scripts/brightness_set.sh 来设置亮度
        # 这里可以使用 subprocess 来调用脚本
        logger.info(f"BrightnessControlTool execute: {kwargs}")
        operation = kwargs.get("operation")
        brightness = kwargs.get("brightness", 10)


        script_path = "/usr/local/bin/brightness_set.sh"
        # 这里要用sudo 来执行脚本
        command = ["sudo", script_path, operation]

        if operation == "set":
            if brightness is None:
                return ToolResult(error="Brightness value is required for 'set' operation.")
            command.append(str(brightness))

        try:
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            output = result.stdout.strip() + result.stderr.strip()
            logger.info(f"Brightness control script output: {output}")
            return ToolResult(output=f"Brightness control executed. Output: {output}")

        except FileNotFoundError:
            error_message = f"Error: Script not found at {script_path}"
            logger.error(error_message)
            return ToolResult(error=error_message)
        except subprocess.CalledProcessError as e:
            error_message = f"Error executing brightness control script: {e}\nStdout: {e.stdout}\nStderr: {e.stderr}"
            logger.error(error_message)
            return ToolResult(error=error_message)
        except Exception as e:
            error_message = f"An unexpected error occurred: {e}"
            logger.error(error_message)
            return ToolResult(error=error_message)

class BatteryInfoTool(BaseTool):
    """
    电池信息工具
    """   

    name: str = "BatteryInfoTool"
    description: str = "Get battery information."
    # properties参数有两个，一个亮度百分比，interger类型，一个enum类型的operation，分别是up，down，set
    parameters: Optional[Dict] = {
        }
    async def execute(self,**kwargs) -> ToolResult:
        """获取电池电量"""
        """使用 /sys/class/power_supply/cw2015-battery/capacity 命令获取电池电量"""
        try:
            with open("/sys/class/power_supply/cw2015-battery/capacity", "r") as f:
                battery_level = f.read().strip()
                logger.info(f"Battery level: {battery_level}%")
                return ToolResult(output=f"Battery level is {battery_level}%.")
        except FileNotFoundError:
            logger.error("Battery information file not found.")
            return ToolResult(error="Battery information file not found.")
        except Exception as e:
            logger.error(f"Error reading battery level: {e}")
            return ToolResult(error=f"Error reading battery level: {e}")

class NetworkTool(BaseTool):
    """
    网络状态工具
    """
    name: str = "NetworkStatusTool"
    description: str = "Get and control network status."
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "operation": {
                "type": "string",
                "enum": ["on", "off", "disconnect", "show", "status"],
                "description": "网络操作类型: on(连接), off(断开), disconnect(断开当前连接), show(显示当前连接), status(查看状态)"
            }
        },
        "required": ["operation"],
    }

    async def execute(self, **kwargs) -> ToolResult:
        """控制网络连接"""
        try:
            operation = kwargs.get("operation")
            
            # 获取 WiFi 设备
            wifi_device = None
            for device in NetworkManager.GetDevices():
                if device.DeviceType == Device.TYPE_WIFI:
                    wifi_device = device
                    break
            
            if not wifi_device:
                return ToolResult(error="No WiFi device found")

            if operation == "off":
                wifi_device.Disconnect()
                return ToolResult(output="WiFi disconnected")
                
            elif operation == "on":
                wifi_device.Connect()
                return ToolResult(output="WiFi connected")
                
            elif operation == "disconnect":
                active_connection = wifi_device.ActiveConnection
                if active_connection:
                    NetworkManager.DeactivateConnection(active_connection)
                    return ToolResult(output=f"Disconnected from {active_connection.Id}")
                return ToolResult(output="No active connection to disconnect")
                
            elif operation == "show" or operation == "status":
                active_connection = wifi_device.ActiveConnection
                if active_connection:
                    strength = wifi_device.AccessPoint.Strength if wifi_device.AccessPoint else 0
                    return ToolResult(output=f"Connected to: {active_connection.Id} (Signal: {strength}%)")
                return ToolResult(output="Not connected to any network")
            
            else:
                return ToolResult(error="Invalid operation")
                
        except Exception as e:
            return ToolResult(error=f"Network operation failed: {str(e)}")


class BluetoothControlTool(BaseTool):
    """
    蓝牙控制工具
    """
    name: str = "BluetoothControlTool"
    description: str = "Control Bluetooth devices."
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "operation": {
                "type": "string",
                "enum": ["on", "off", "scan", "connect", "disconnect"],
                "description": "蓝牙操作类型: on(打开), off(关闭), scan(扫描设备), connect(连接设备), disconnect(断开设备)"
            },
            "device_address": {
                "type": "string",
                "description": "蓝牙设备地址(仅在connect操作时需要)"
            }
        },
        "required": ["operation"],
    }

    async def execute(self, **kwargs) -> ToolResult:
        """控制蓝牙设备"""
        try:
            operation = kwargs.get("operation")
            device_address = kwargs.get("device_address")
            
            if operation == "on":
                return await self._enable_bluetooth()
            elif operation == "off":
                return await self._disable_bluetooth()
            elif operation == "scan":
                return await self._scan_devices()
            elif operation == "connect":
                if not device_address:
                    return ToolResult(error="Device address is required for connect operation")
                return await self._connect_device(device_address)
            elif operation == "disconnect":
                return await self._disconnect_device()
            else:
                return ToolResult(error="Invalid operation")
        except Exception as e:
            logger.error(f"Bluetooth operation failed: {str(e)}")
            return ToolResult(error=f"Bluetooth operation failed: {str(e)}")

    async def _enable_bluetooth(self) -> ToolResult:
        """启用蓝牙"""
        try:
            subprocess.run(["rfkill", "unblock", "bluetooth"], check=True)
            return ToolResult(output="Bluetooth enabled successfully")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to enable Bluetooth: {str(e)}")

    async def _disable_bluetooth(self) -> ToolResult:
        """禁用蓝牙"""
        try:
            subprocess.run(["rfkill", "block", "bluetooth"], check=True)
            return ToolResult(output="Bluetooth disabled successfully")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to disable Bluetooth: {str(e)}")

    async def _scan_devices(self) -> ToolResult:
        """扫描蓝牙设备"""
        try:
            devices = await BleakScanner.discover()
            if not devices:
                return ToolResult(output="No Bluetooth devices found")
            
            device_list = []
            for device in devices:
                device_info = f"Name: {device.name}, Address: {device.address}"
                device_list.append(device_info)
            
            return ToolResult(output="\n".join(device_list))
        except Exception as e:
            return ToolResult(error=f"Failed to scan devices: {str(e)}")

    async def _connect_device(self, device_address: str) -> ToolResult:
        """连接蓝牙设备"""
        try:
            async with BleakClient(device_address) as client:
                if client.is_connected:
                    return ToolResult(output=f"Successfully connected to device: {device_address}")
                else:
                    return ToolResult(error=f"Failed to connect to device: {device_address}")
        except Exception as e:
            return ToolResult(error=f"Connection failed: {str(e)}")

    async def _disconnect_device(self) -> ToolResult:
        """断开蓝牙设备"""
        try:
            subprocess.run(["bluetoothctl", "disconnect"], check=True)
            return ToolResult(output="Device disconnected successfully")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to disconnect device: {str(e)}")


class SystemInfoTool(BaseTool):
    """
    系统信息工具
    """
    name: str = "SystemInfoTool"
    description: str = "Get system information like OS version, CPU usage, etc."
    parameters: Optional[Dict] = {} # SystemInfoTool 似乎不需要参数

    async def execute(self,**kwargs) -> ToolResult:
        """获取系统信息"""
        return ToolResult(output="System info: OS version, CPU usage, etc.")
    
class SystemShutdownTool(BaseTool):
    """
    系统关机工具
    """
    name: str = "SystemShutdownTool"
    description: str = "Shut down the system.调用前应该使用AskHuman工具先询问用户确认重启操作。请在参数中设置 confirm 为 true 来确认关机。默认情况下，confirm 为 false，不会执行关机操作。"
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "confirm": {
                "type": "boolean",
                "description": "是否确认关机，默认为 false"
            }
        },
        "required": [],
    }

    async def execute(self, **kwargs) -> ToolResult:
        """执行系统关机"""
        confirm = kwargs.get("confirm", False)
        if not confirm:
            return ToolResult(error="Shutdown not confirmed. Set 'confirm' to true to proceed.")
        
        try:
            subprocess.run(["sudo", "shutdown", "-h", "now"], check=True)
            return ToolResult(output="System is shutting down.")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to shut down the system: {str(e)}")

class SystemRebootTool(BaseTool):
    """
    系统重启工具
    """
    name: str = "SystemRebootTool"
    description: str = "Reboot the system.调用前应该使用AskHuman工具先询问用户确认重启操作。请在参数中设置 confirm 为 true 来确认重启。默认情况下，confirm 为 false，不会执行重启操作。"
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "confirm": {
                "type": "boolean",
                "description": "是否确认重启，默认为 false"
            }
        },
        "required": [],
    }

    async def execute(self, **kwargs) -> ToolResult:
        """执行系统重启"""
        confirm = kwargs.get("confirm", False)
        if not confirm:
            return ToolResult(error="Reboot not confirmed. Set 'confirm' to true to proceed.")
        
        try:
            subprocess.run(["reboot"], check=True)
            return ToolResult(output="System is rebooting.")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to reboot the system: {str(e)}")


class OpenAppsTool(BaseTool):
    """
    打开应用工具
    app_name: str
    """
    name: str = "OpenAppsTool"
    description: str = "启动Linux桌面应用程序。参数app_name必须是从desktop://available-apps资源中获取的有效路径。应用列表如下："
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "app_name": {
                "type": "string",
                "description": "应用对应的desktop文件名,,可以先通过Resource读取所有desktop 文件列表,然后选择对应的文件名。",

            }
        },
        "required": ["app_name"],
    }
    uri: str = "/usr/share/applications/"
    
    def __init__(self):
        super().__init__()
        # 在初始化时更新 description
        self.description = f"{self.description}\n{self.get_desktop_files()}"


    def get_desktop_files(self) -> str:

        """
        获取 /usr/share/applications/ 目录下的所有 .desktop 文件列表
        Returns:
            str: 所有desktop文件列表，每行一个文件名
        """
        try:
            desktop_files = []
            # 遍历 /usr/share/applications/ 目录
            for file in os.listdir(self.uri):
                if file.endswith(".desktop"):
                    desktop_files.append(file)
            
            if not desktop_files:
                return "No desktop files found."
            
            # 按字母顺序排序
            desktop_files.sort()
            
            # 拼接结果字符串
            result = "Available desktop applications:\n"
            for i, file in enumerate(desktop_files, 1):
                result += f"{i}. {file}\n"
                
            return result
        except Exception as e:
            logger.error(f"Error reading desktop files: {e}")
            return f"Error reading desktop files: {str(e)}"



    async def execute(self, **kwargs) -> ToolResult:
        """打开应用"""
        default_app_path = "/usr/share/applications/"
        

        # 使用exo-open命令打开应用
        try:
            # 检查一下$DIAPLAY环境变量是否设置，如果没有设置，则手动设置环境变量
            if "DISPLAY" not in os.environ:
                os.environ["DISPLAY"] = ":0"  # 设置默认的显示器
            app_name = kwargs.get("app_name")
            if not app_name:
                return ToolResult(error="Application name is required.")
            app_path = f"{default_app_path}{app_name}"
            if not app_name.endswith(".desktop"):
                app_path += ".desktop"
            if not os.path.exists(app_path):
                return ToolResult(error=f"Application {app_name} not found in {default_app_path}.")
            logger.info(f"Opening application: {app_path}")
            subprocess.run(["exo-open", app_path], check=True)
            return ToolResult(output=f"Opened application: {app_name}")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to open application: {str(e)}")

class CloseAppsTool(BaseTool):
    """
    关闭应用工具
    process_name: str
    """
    name: str = "CloseAppsTool"
    description: str = "关闭Linux桌面应用程序。参数process_name必须是当前用户正在运行的进程名，所有进程列表如下："
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "process_name": {
                "type": "string",
                "description": "应用对应的进程名,例如：chrome, Thunar等",

            }
        },
        "required": ["process_name"],
    }

    def __init__(self):
        super().__init__()
        # 在初始化时更新 description
        self.description = f"{self.description}\n{self.get_process_list()}"

    def get_process_list(self) -> str:

        """
        获取 当前用户正在运行的进程列表
        Returns:
            str: 所有进程列表，每行一个进程名
        """
        try:
            process_list = []
            # 使用ps命令获取当前用户的进程列表
            result = subprocess.run(["ps", "-u", os.getlogin(), "-o", "comm="], capture_output=True, text=True)
            if result.returncode != 0:
                return "Error retrieving process list."
            
            # 按行分割输出结果
            for line in result.stdout.strip().split("\n"):
                if line:  # 确保行不为空
                    process_list.append(line.strip())
            
            if not process_list:
                return "No processes found."
            
            # 拼接结果字符串
            result_str = "Running processes:\n"
            for i, process in enumerate(process_list, 1):
                result_str += f"{i}. {process}\n"
                
            return result_str
        except Exception as e:
            logger.error(f"Error reading process list: {e}")
            return f"Error reading process list: {str(e)}"


    
    async def execute(self, **kwargs) -> ToolResult:
        """关闭应用"""

        try:
            process_name = kwargs.get("process_name")
            if not process_name:
                return ToolResult(error="Application name is required.")
            # 使用pkill命令关闭应用
            logger.info(f"Closing application: {process_name}")
            subprocess.run(["pkill", "-f",process_name], check=True)
            return ToolResult(output=f"Successfully Closed application: {process_name}")
        except subprocess.CalledProcessError as e:
            return ToolResult(error=f"Failed to open application: {str(e)}")


class TimeTool(BaseTool):
    """
    时间转换工具
    """
    name: str = "TimeTool"
    description: str = "转换格式化时间为时间戳,默认格式化时间为'%Y-%m-%d %H:%M:%S'。如果需要其他格式，请在参数中指定format。"
    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "format": {
                "type": "string",
                "description": "时间格式化字符串，默认为 '%Y-%m-%d %H:%M:%S'。"
            },
            "datetime": {
                "type": "string",
                "description": "需要转换的时间字符串，默认为当前时间。"
            }
        },
        "required": [],
    }
    async def execute(self, **kwargs) -> ToolResult:
        """将格式化时间转换为时间戳"""
        from datetime import datetime
        import time

        format_str = kwargs.get("format", "%Y-%m-%d %H:%M:%S")
        datetime_str = kwargs.get("datetime", None)

        if datetime_str is None:
            # 如果没有提供时间字符串，则使用当前时间
            current_time = datetime.now()
        else:
            try:
                current_time = datetime.strptime(datetime_str, format_str)
            except ValueError as e:
                return ToolResult(error=f"Invalid datetime format: {e}")

        # 转换为 Unix 时间戳
        timestamp = int(time.mktime(current_time.timetuple()))
        return ToolResult(output=f"Timestamp: {timestamp}")



class FileSearchTool(BaseTool):
    """
    文件搜索工具，可以根据文件名、文件类型、文件内容、大小等条件搜索文件。
    """
    name: str = "FileSearchTool"
    description: str = """
        工具名称：文件搜索工具
        工具用途：可以根据文件名、文件类型、文件内容、大小等条件搜索文件。
        返回结果遵循：搜索结果请返回Markdown格式的字符串或者超链接，以支持直接打开文件。
        时间处理：如果遇到基于时间类的查询，请先调用TimeTool工具，准确转换时间戳。我会告诉你当前时间，请忽略模型本身的时间。
        SQL查询参数： 请认真生成查询参数，避免出现参数错误而导致的查询失败。尤其是在处理keyword相关参数时，不要使用通配符或正则表达式。请确保参数的正确性和安全性。
        文件扩展名：如果查找文本文件，应该使用extensions参数指定文件扩展名，例如['.txt', '.md']。如果查找图片文件，应该使用extensions参数指定图片扩展名，例如['.jpg', '.png']。如果查找Python代码文件，应该使用extensions参数指定Python扩展名，例如['.py']。
        文件分类： 例如：文本文件，图片文件，Python代码文件等。请根据实际需要使用extensions参数指定文件扩展名。"""

    parameters: Optional[Dict] = {
        "type": "object",
        "properties": {
            "filename_keyword": {
                "type": "string",
                "description": "文件名关键词，不应该包含通配符或正则表达式。可以是文件名的一部分，例如 'report' 或 'data'。"
            },
            "extensions": {
                "type": "array",
                "items": {"type": "string"},
                "description": "文件扩展名列表 (例如: ['.py', '.txt', '.jpg'])"
            },
            "text_content_keyword": {
                "type": "string",
                "description": "文件内容关键词，不应该包含通配符或正则表达式。可以是文件内容的一部分。"
            },
            "min_size": {
                "type": "integer",
                "description": "最小文件大小 (字节)"
            },
            "max_size": {
                "type": "integer",
                "description": "最大文件大小 (字节)"
            },
            "min_created_time": {
                "type": "number",
                "description": "最小创建时间 (Unix 时间戳)"
            },
            "max_created_time": {
                "type": "number",
                "description": "最大创建时间 (Unix 时间戳)"
            },
            "min_modified_time": {
                "type": "number",
                "description": "最小修改时间 (Unix 时间戳)"
            },
            "max_modified_time": {
                "type": "number",
                "description": "最大修改时间 (Unix 时间戳)"
            },
            "is_dir": {
                "type": "boolean",
                "description": "是否为目录 (true 表示目录, false 表示文件)"
            },
            "filepath_keyword": {
                "type": "string",
                "description": "文件路径关键词，不应该包含通配符或正则表达式。可以是文件路径的一部分，例如 '/home/<USER>/' 或 '/data/'。"
            },
            "limit": {
                "type": "integer",
                "description": "返回结果数量限制 (默认 100)"
            }
        },
        "required": [], # All parameters are optional for a broad search
    }

    async def execute(self, **kwargs) -> ToolResult:
        """
        根据提供的参数搜索文件。
        """
        logger.info(f"FileSearchTool execute: {kwargs}")
        try:
            results = searcher.search_files(
                filename_keyword=kwargs.get("filename_keyword"),
                extensions=kwargs.get("extensions"),
                text_content_keyword=kwargs.get("text_content_keyword"),
                min_size=kwargs.get("min_size"),
                max_size=kwargs.get("max_size"),
                min_created_time=kwargs.get("min_created_time"),
                max_created_time=kwargs.get("max_created_time"),
                min_modified_time=kwargs.get("min_modified_time"),
                max_modified_time=kwargs.get("max_modified_time"),
                is_dir=kwargs.get("is_dir"),
                filepath_keyword=kwargs.get("filepath_keyword"),
                limit=kwargs.get("limit", 100)
            )

            if not results:
                return ToolResult(output="No files found matching the criteria.")
            
            output_lines = ["Found files:"]
            for i, file_info in enumerate(results):
                file_type = "目录" if file_info['is_dir'] else "文件"
                
                # 确保 filename 和 filepath 是字符串
                filename = str(file_info['filename'])
                filepath = str(file_info['filepath'])

                output_line_raw = f"{i+1}. [{file_type}] 路径: [{filename}](file://{filepath})"
                
                # 尝试对单行进行 JSON 往返处理，禁用 ASCII 转义
                try:
                    processed_output_line = json.loads(json.dumps(output_line_raw, ensure_ascii=False))
                except Exception as e:
                    logger.warning(f"Failed to process output line for display: {e}. Using original.")
                    processed_output_line = output_line_raw

                output_lines.append(processed_output_line)
            
            final_output_string = "\n".join(output_lines)
            
            # 再次对最终的输出字符串进行 JSON 往返处理，确保其不含转义
            try:
                final_output_string_processed = json.loads(json.dumps(final_output_string, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"Failed to process final output string: {e}. Using original.")
                final_output_string_processed = final_output_string

            return ToolResult(output=final_output_string_processed)

        except Exception as e:
            logger.error(f"Error in FileSearchTool: {e}")
            return ToolResult(error=f"Error searching files: {e}")
