from enum import Enum
from typing import Any, List, Literal, Optional, Union

from pydantic import BaseModel, Field

from app.logger import logger

class Role(str, Enum):
    """定义消息的角色类型"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"           # 工具/函数调用结果

ROLE_VALUES = tuple(role.value for role in Role)
ROLE_TYPE = Literal[ROLE_VALUES]  # type: ignore

class ToolChoice(str, Enum):
    """定义工具调用的选择策略"""
    NONE = "none"           # 不使用工具
    AUTO = "auto"           # 自动选择工具
    REQUIRED = "required"   # 强制使用工具

TOOL_CHOICE_VALUES = tuple(choice.value for choice in ToolChoice)
TOOL_CHOICE_TYPE = Literal[TOOL_CHOICE_VALUES]  # type: ignore

class AgentState(str, Enum):
    """代理（agent）的执行状态。枚举值：空闲、运行、完成、错误。"""
    IDLE = "IDLE"
    RUNNING = "RUNNING"
    WAITING_FOR_HUMAN_INPUT = "waiting_for_human_input"
    FINISHED = "FINISHED"
    ERROR = "ERROR"

class Function(BaseModel):
    '''用于 ToolCall 中，描述具体函数调用'''
    name: str
    arguments: str

class ToolCall(BaseModel):
    '''表示消息中的工具调用'''
    id: str
    type: str = "function"
    function: Function

class Message(BaseModel):
    '''表示聊天消息的消息。统一表示所有类型的消息，支持文本、工具调用和图像。'''
    role: ROLE_TYPE = Field(...) # type: ignore     # 消息角色（system, user, assistant, tool），必填。
    content: Optional[str] = Field(default=None)    # 消息内容（字符串，可为空）。
    tool_calls: Optional[List[ToolCall]] = Field(default=None)      # 工具调用列表（可选，如助手消息包含函数调用）。
    name: Optional[str] = Field(default=None)       # 消息名称（可选，用于区分）。
    tool_call_id: Optional[str] = Field(default=None)               # 工具调用 ID（可选，与 tool 角色相关）。
    base64_image: Optional[str] = Field(default=None)               # Base64 编码的图像（可选，支持多模态）。
    image_urls: Optional[List] = Field(default=None)           # 图像url
    
    # 运算符重载

    # 转换方法
    def to_dict(self) -> dict:
        '''将 Message 转为字典，排除空字段。(与 OpenAI API 等交互时，使用字典格式。)'''
        message = {"role": self.role}
        if self.content is not None:
            message["content"] = self.content
        if self.tool_calls is not None:
            message["tool_calls"] = [tool_call.model_dump() for tool_call in self.tool_calls]
        if self.name is not None:
            message["name"] = self.name
        if self.tool_call_id is not None:
            message["tool_call_id"] = self.tool_call_id
        if self.base64_image is not None:
            message["base64_image"] = self.base64_image
        if self.image_urls is not None:
            message["image_urls"] = self.image_urls
        return message


    # 工厂方法
    @classmethod
    def user_message(cls, content: str, base64_image: Optional[str] = None, image_urls: Optional[List] = None) -> "Message":
        '''创建用户消息，支持图像。'''
        return cls(role=Role.USER, content=content, base64_image=base64_image, image_urls=image_urls)
    
    @classmethod
    def system_message(cls, content: str) -> "Message":
        """创建系统消息。"""
        return cls(role=Role.SYSTEM, content=content)

    @classmethod
    def assistant_message(
        cls, content: Optional[str] = None, base64_image: Optional[str] = None
    ) -> "Message":
        """创建助手消息，支持空内容。"""
        return cls(role=Role.ASSISTANT, content=content, base64_image=base64_image)

    @classmethod
    def from_tool_calls(
        cls,
        tool_calls: List[Any],
        content: Union[str, List[str]] ="",
        base64_image: Optional[str] = None,
        **kwargs
    ) -> "Message":
        '''
        从原始工具调用数据创建助手消息。
        逻辑：
            将工具调用转为 ToolCall 兼容格式（id, type, function）。
        参数：
            tool_calls：LLM 返回的工具调用。
            content：消息内容（可选）
            base64_image：base64编码的图片（可选）
        返回：
            助手消息，包含工具调用和可选内容/图像。
        '''
        formatted_calls = [
            ToolCall(
                id=call.id,
                function=Function(**call.function.model_dump()),
                type="function"
            )
            for call in tool_calls
        ]
        
        processed_content = "\n".join(content) if isinstance(content, list) else content

        return cls(
            role=Role.ASSISTANT,
            content=processed_content,
            tool_calls=formatted_calls,
            base64_image=base64_image,
            **kwargs,
        )

    @classmethod
    def tool_message(cls, content: str, name: str, tool_call_id: str, base64_image: Optional[str] = None) -> "Message":
        '''创建工具消息，需指定 name 和 tool_call_id。'''
        return cls(
            role=Role.TOOL,
            content=content,
            name=name,
            tool_call_id=tool_call_id,
            base64_image=base64_image,
        )
