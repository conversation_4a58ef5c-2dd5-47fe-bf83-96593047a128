import json
import time
import requests
from typing import List, Optional, Union, Dict, Any, cast

from pydantic import model_validator
from app.llm.llm import LLM
from app.logger import logger
from app.agent.base_agent import BaseAgent
from app.prompt.video import USER_PROMPT, SYSTEM_PROMPT
from app.schema import AgentState, Message
from app.utils.event_manager import EventManager

from app.config import config

class VideoAgent(BaseAgent):
    """
    AI视频生成助手，根据用户描述生成视频。
    """    
    
    name: str = "video_agent"
    description: str = """你是一个AI视频生成助手，根据用户描述生成视频。功能包括：
    1. 根据用户的文本描述生成高质量的视频。
    2. 支持多种视频风格和场景描述。
    3. 支持使用参考图像生成视频。
    """
    system_prompt: Optional[str] = SYSTEM_PROMPT
    user_prompt: Optional[str] = USER_PROMPT
    next_step_prompt: Optional[str] = "需要我为您做些什么？"

    @model_validator(mode="after")
    def initialize_agent(self) -> "VideoAgent":
        # 使用一个适合生成文本Prompt的模型
        self.llm = LLM(config_name="default")
        return self

    async def run(
        self,
        user_id: str,
        request: Optional[str] = None,
        image_urls: Optional[List[str]] = None,
        with_history: bool = True,
        event_manager: Optional[EventManager] = None,
        session_id: Optional[str] = None,
    ) -> str:
        if not request:
            return json.dumps({
                "message": "请提供您想要生成的视频描述。",
                "media_url": None
            }, ensure_ascii=False)

        # 1. 获取用户会话
        if with_history:
            memory = self.session_storage.get_session(user_id=user_id)
            self.memory = memory
            self.memory.update_memory("user", request)

        try:
            # 在生成视频Prompt之前发布思考事件
            if event_manager:
                await event_manager.publish(user_id, "thought", {"agent_name": self.name, "thought_process": "正在理解您的需求并生成视频描述..."}, session_id)
            
            # 2. 准备发送给 LLM 的消息            
            prompt = self.user_prompt if self.user_prompt else ""
            user_message = Message.user_message(content=prompt.format(request=request)).to_dict()
            messages = cast(List[Union[Dict[str, Any], Message]], [user_message])
            
            # 只有当 system_prompt 不为 None 时才添加系统消息
            system_msgs = None
            if self.system_prompt:
                system_message = Message.system_message(content=self.system_prompt).to_dict()
                system_msgs = cast(List[Union[Dict[str, Any], Message]], [system_message])

            # 3. 调用 LLM 生成视频 Prompt
            logger.info(f"Calling LLM to generate video prompt for request: {request}")
            video_prompt = await self.llm.ask(
                messages=messages,
                system_msgs=system_msgs,
                stream=False
            )
            logger.info(f"Generated video prompt: {video_prompt}")

            if not video_prompt:
                return json.dumps({
                    "message": "未能生成有效的视频Prompt，请尝试更具体的描述。",
                    "media_url": None
                }, ensure_ascii=False)

            # 在调用视频生成API之前发布思考事件
            if event_manager:
                await event_manager.publish(user_id, "thought", {"agent_name": self.name, "thought_process": f"视频描述生成完毕，正在调用视频模型进行生成..."}, session_id)

            # 4. 调用视频生成 API
            logger.info(f"Calling video generation API with prompt: {video_prompt}")
            
            # 获取视频生成配置
            video_generation_config = config.video_generation
            if not video_generation_config:
                raise ValueError("Video generation configuration not found")

            # 准备请求参数
            payload = {
                "model": video_generation_config.default_model,
                "input": {
                    "prompt": video_prompt
                },
                "parameters": {
                    "size": "1280*720"
                }
            }
            
            # 如果提供了参考图像，添加到请求中
            if image_urls and len(image_urls) > 0:
                payload["input"]["image"] = image_urls[0]

            headers = {
                "Authorization": f"Bearer {video_generation_config.api_key}",
                "Content-Type": "application/json",
                "X-DashScope-Async": "enable"
            }

            # 发布 tool_call_start 事件
            if event_manager:
                await event_manager.publish(user_id, "tool_call_start", {"agent_name": self.name, "tool_name": "video_generation_api", "tool_input": payload}, session_id)

            # 发送生成请求
            request_url = f"{video_generation_config.base_url}"
            logger.debug(f"Sending video generation request to URL: {request_url}")
            logger.debug(f"Request Headers: {headers}")
            logger.debug(f"Request Payload: {json.dumps(payload, ensure_ascii=False)}")

            response = requests.post(
                request_url,
                data=json.dumps(payload, ensure_ascii=False).encode('utf-8'),
                headers=headers
            )
            logger.debug(f"Received video generation response with status code: {response.status_code}")
            logger.debug(f"Response Body: {response.text}")
            response.raise_for_status()
            
            submit_data = response.json()
            request_id = submit_data.get("request_id")
            task_id = submit_data.get("output", {}).get("task_id")
            
            if not request_id or not task_id:
                logger.error(f"Video generation API failed to return request_id or task_id: {submit_data}")
                return json.dumps({
                    "message": "视频生成请求失败，未获取到request_id或task_id。",
                    "media_url": None
                }, ensure_ascii=False)

            # 5. 轮询获取生成结果
            max_retries = 60  # 最多等待10分钟
            retry_interval = 10  # 每10秒检查一次
            
            for i in range(max_retries):
                # 在每次轮询时发布思考事件
                if event_manager:
                    await event_manager.publish(user_id, "thought", {"agent_name": self.name, "thought_process": f"视频生成中，请稍候... (进度: {i+1}/{max_retries})"}, session_id)

                status_url = f"https://dashscope.aliyuncs.com/api/v1/tasks/{task_id}"
                logger.info(f"Polling video generation status from URL: {status_url}")

                status_response = requests.get( # 改为 GET 请求
                    status_url,
                    headers=headers
                )
                logger.debug(f"Received polling response with status code: {status_response.status_code}")
                logger.debug(f"Polling Response Body: {status_response.text}")
                status_response.raise_for_status()
                status_data = status_response.json()
                
                task_status = status_data.get("output", {}).get("task_status")
                
                if task_status == "SUCCEEDED": # 注意这里是SUCCEED，不是Succeed
                    video_url = status_data["output"]["video_url"] # 假设视频URL在这里
                    logger.info(f"Video generated successfully at URL: {video_url}")
                    result = {"message": "视频生成成功！", "media_url": video_url}
                    # 发布 tool_call_end 事件（成功）
                    if event_manager:
                        await event_manager.publish(user_id, "tool_call_end", {"agent_name": self.name, "tool_name": "video_generation_api", "tool_output": result}, session_id)
                    return json.dumps(result, ensure_ascii=False)
                elif task_status == "FAILED": # 注意这里是FAILED，不是Failed
                    reason = status_data.get('output', {}).get('reason', '未知错误')
                    logger.error(f"Video generation failed: {reason}. Full status data: {status_data}")
                    result = {"message": f"视频生成失败：{reason}", "media_url": None}
                    # 发布 tool_call_end 事件（失败）
                    if event_manager:
                        await event_manager.publish(user_id, "tool_call_end", {"agent_name": self.name, "tool_name": "video_generation_api", "tool_output": {"error": reason}}, session_id)
                    return json.dumps(result, ensure_ascii=False)
                
                time.sleep(retry_interval)
            
            # 发布 tool_call_end 事件（超时）
            timeout_result = {"message": "视频生成超时，请稍后使用相同的提示词重试。", "media_url": None}
            if event_manager:
                await event_manager.publish(user_id, "tool_call_end", {"agent_name": self.name, "tool_name": "video_generation_api", "tool_output": {"error": "Timeout"}}, session_id)
            return json.dumps(timeout_result, ensure_ascii=False)

        except requests.exceptions.RequestException as e:
            error_message = f"Video generation API request failed: {e}"
            if e.response is not None:
                error_message += f", Status Code: {e.response.status_code}, Response Body: {e.response.text}"
            logger.error(error_message)
            self.state = AgentState.ERROR
            result = {"message": f"调用视频生成API失败: {e}", "media_url": None}
            # 发布工具调用结束事件（API请求失败）
            if event_manager:
                await event_manager.publish(user_id, "tool_call_end", {"agent_name": self.name, "tool_name": "video_generation_api", "tool_output": {"error": error_message}}, session_id)
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            logger.error(f"VideoAgent run error: {e}")
            self.state = AgentState.ERROR
            result = {"message": f"视频生成过程中发生错误: {e}", "media_url": None}
            # 发布工具调用结束事件（未知错误）
            if event_manager:
                await event_manager.publish(user_id, "tool_call_end", {"agent_name": self.name, "tool_name": "video_generation_api", "tool_output": {"error": str(e)}}, session_id)
            return json.dumps(result, ensure_ascii=False)
