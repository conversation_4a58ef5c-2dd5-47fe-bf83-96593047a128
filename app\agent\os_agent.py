from app.logger import logger
from app.agent.mcp_agent import ToolCallAgent
from app.prompt.os import SYSTEM_PROMPT as OS_SYSTEM_PROMPT,NEXT_STEP_PROMPT
from app.tool.mcp_tool_collection import McpToolCollection
from pydantic import Field
from app.schema import Message
from typing import Any, Dict, List, Optional, Tuple 
from app.utils.event_manager import EventManager

class OSAgent(ToolCallAgent):
    """OSAgent 是一个系统级 agent，主要用于帮助用户解释和处理系统问题，以及执行预定的一些工具来完成用户的指令。"""

    name: str = "os_agent"
    description: str = "A system-level agent for handling system issues and executing predefined tools."

    system_prompt: str = OS_SYSTEM_PROMPT

    next_step_prompt: str = NEXT_STEP_PROMPT

    # TODO: Add any specific initialization or methods for OSAgent
    # 可用工具列表,覆盖父类的 ToolCollection。
    # 同时,用于管理 MCP 服务器连接和工具。
    available_tools: McpToolCollection = Field(default_factory=McpToolCollection) # type: ignore
    # 连接方式（"stdio" 或 "sse"），默认使用 stdio。
    connection_type: str = "stdio"



    @classmethod
    async def create(
        cls,
        connection_type: Optional[str] = None,
        server_url: Optional[str] = None,
        command: Optional[str] = None,
        args: Optional[List[str]] = None,
    ) -> "OSAgent":
        """
        异步工厂方法，用于创建并初始化 MCPAgent 实例。

        Args:
            connection_type: 连接类型 ("stdio" 或 "sse")
            server_url: MCP 服务器的 URL（用于 SSE 连接）
            command: 要运行的命令（用于 stdio 连接）
            args: 命令参数（用于 stdio 连接）
            **kwargs: 其他传递给 MCPAgent 构造函数的参数

        Returns:
            MCPAgent: 已初始化的代理实例

        Raises:
            ValueError: 如果连接参数无效
        """
        # 创建实例
        agent = cls()
        # 异步初始化
        await agent.initialize(
            connection_type=connection_type,
            server_url=server_url,
            command=command,
            args=args
        )
        return agent

    async def initialize(
        self,
        connection_type: Optional[str] = None,
        server_url: Optional[str] = None,
        command: Optional[str] = None,
        args: Optional[List[str]] = None,
    ) -> None:    
        """初始化 MCP 服务器连接，设置可用工具。
        1.建立 MCP 连接，动态加载服务器工具。
        2.初始化工具信息到记忆，供 LLM 使用。

        Args:
            connection_type: Type of connection to use ("stdio" or "sse")
            server_url: URL of the MCP server (for SSE connection)
            command: Command to run (for stdio connection)
            args: Arguments for the command (for stdio connection)
        """
        if connection_type:
            self.connection_type = connection_type

        # 连接服务器：
        if self.connection_type == "sse":
            if not server_url:
                raise ValueError("Server URL is required for SSE connection")
            # sse：调用 mcp_clients.connect_sse(server_url)，需要 server_url。
            await self.available_tools.connect_sse(server_url=server_url)
        elif self.connection_type == "stdio":
            if not command:
                raise ValueError("Command is required for stdio connection")
            # stdio：调用 mcp_clients.connect_stdio(command, args)，需要 command。
            await self.available_tools.connect_stdio(command=command, args=args or [])
        else:
            # 无效类型抛出 ValueError。
            raise ValueError(f"Unsupported connection type: {self.connection_type}")

        # 过滤工具：只保留 agent_name 匹配 self.name 的工具
        filtered_tools = [
            tool for tool in self.available_tools.tools
            if getattr(tool, "agent_name", "unknown") == self.name
        ]
        # 更新 available_tools 的 tools 和 tool_map
        self.available_tools.tools = tuple(filtered_tools)
        # self.available_tools.tool_map = {tool.name: tool for tool in filtered_tools}
        self.available_tools.tool_map = {}
        for tool in filtered_tools:
            # 拆分 name 字段，格式为 agent_name:tool_name
            if ":" in tool.name:
                agent_name, tool_name = tool.name.split(":", 1)
            else:
                agent_name = "unknown"
                tool_name = tool.name
            self.available_tools.tool_map[tool_name] = tool

        # 记录日志
        tool_names = list(self.available_tools.tool_map.keys())
        if not tool_names:
            logger.warning(f"No tools available for agent {self.name}")
        else:
            logger.info(f"Filtered tools for {self.name}: {tool_names}")

        # 刷新工具：
        # 调用 _refresh_tools，获取初始工具列表和模式。
        # await self._refresh_tools()

        # 添加提示：
        # 将可用工具信息添加到 memory 作为系统消息。
        # tool_names = list(self.available_tools.tool_map.keys())
        # tools_info = ", ".join(tool_names)
        # self.memory.add_message(
        #     Message.system_message(f"{self.system_prompt}\n\nAvailable MCP tools: {tools_info}")
        # )


    async def _refresh_tools(self) -> Tuple[List[str], List[str]]:
        """定期从 MCP 服务器获取工具列表，检测新增、移除或变更的工具。
        Returns:
            A tuple of (added_tools, removed_tools)
        """
        # 获取当前活跃的会话
        session = None
        if self.available_tools.sessions:
            # 假设只有一个活跃的会话，或者取第一个会话
            session = next(iter(self.available_tools.sessions.values()), None)

        if not session:
            return [], []

        # 获取工具列表：
        # 调用 mcp_clients.session.list_tools() 获取当前工具。
        response = await session.list_tools()
        # 提取工具名称和参数模式（inputSchema）。
        current_tools = {tool.name: tool.inputSchema for tool in response.tools}

        # 比较变化：
        # 与 tool_schemas 比较，计算新增（added_tools）、移除（removed_tools）和变更（changed_tools）的工具。
        current_names = set(current_tools.keys())
        previous_names = set(self.tool_schemas.keys())
        added_tools = list(current_names - previous_names)
        removed_tools = list(previous_names - current_names)
        changed_tools = []
        for name in current_names.intersection(previous_names):
            if current_tools[name] != self.tool_schemas.get(name):
                changed_tools.append(name)
        # 更新状态：
        # 更新 tool_schemas。
        self.tool_schemas = current_tools
        # 记录变化日志。
        # 将变化信息添加到 memory 作为系统消息。
        # 返回结果：
        # 返回 (added_tools, removed_tools) 元组。
        if added_tools:
            logger.info(f"Added MCP tools: {added_tools}")
            self.memory.add_message(
                Message.system_message(f"New tools available: {', '.join(added_tools)}")
            )
        if removed_tools:
            logger.info(f"Removed MCP tools: {removed_tools}")
            self.memory.add_message(
                Message.system_message(
                    f"Tools no longer available: {', '.join(removed_tools)}"
                )
            )
        if changed_tools:
            logger.info(f"Changed MCP tools: {changed_tools}")

        return added_tools, removed_tools

    async def cleanup(self, server_id: str) -> None:
        """Clean up MCP connection when done."""
        if server_id and server_id in self.available_tools.sessions:
            await self.available_tools.disconnect(server_id)
            logger.info(f"MCP connection for server {server_id} closed")
        elif not server_id and self.available_tools.sessions:
            # If no specific server_id is provided, disconnect all sessions
            await self.available_tools.disconnect()
            logger.info("All MCP connections closed")

    async def run(self, user_id: str, 
                  request: Optional[str] = None, 
                  image_urls: Optional[List[str]] = None, 
                  with_history: bool = True, 
                  event_manager: Optional[EventManager] = None,
                  session_id: Optional[str] = None,
                  server_id: str = "") -> str:
        """
        运行 OSAgent，处理用户请求并返回响应。

        Args:
            user_id: 用户 ID，用于标识请求。
            request: 可选的用户请求，默认为 None。

        Returns:
            str: 处理后的响应。
        """
        """覆盖 ToolCallAgent 的 run，确保 MCP 资源清理。"""
        try:
            # 1.更新系统提示
            # 将可用工具信息添加到 memory 作为系统消息。
            tool_names = list(self.available_tools.tool_map.keys())
            tools_info = ", ".join(tool_names)
            self.system_prompt = f"{self.system_prompt}\n\nAvailable MCP tools: {tools_info}"
            # 2.调用BaseAgent的run方法
            if self.event_manager:
                await self.event_manager.publish(user_id, "thought", {"agent_name": self.name, "thought_process": "正在调用工具..."}, session_id)
            result = await super().run(user_id, request,None,with_history)
            return result
        except Exception as e:
            await self.cleanup(server_id)
            raise
