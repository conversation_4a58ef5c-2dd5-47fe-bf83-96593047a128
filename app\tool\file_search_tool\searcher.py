from typing import List, Optional
import sqlite3
import os
import time # Added import for time
from app.logger import logger

DATABASE_NAME = 'file_index.db'

def get_db_connection(root_dir):
    """Establishes a connection to the SQLite database."""
    # 确保使用绝对路径
    root_dir = os.path.abspath(root_dir)
    
    # 创建数据库目录路径
    db_dir = os.path.join(root_dir, ".local/share/file_search")
    db_path = os.path.join(db_dir, DATABASE_NAME)

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row # This allows accessing columns by name
    return conn

def search_files(
    filename_keyword: Optional[str] = None,
    extensions: Optional[List[str]] = None,
    text_content_keyword: Optional[str] = None,
    min_size: Optional[int] = None,
    max_size: Optional[int] = None,
    min_created_time: Optional[float] = None, # New parameter
    max_created_time: Optional[float] = None, # New parameter
    min_modified_time: Optional[float] = None, # New parameter
    max_modified_time: Optional[float] = None, # New parameter
    is_dir: Optional[bool] = None,
    filepath_keyword: Optional[str] = None, # Added parameter for file path search
    limit: int = 100
) -> List[dict]:
    """
    Searches the file index based on various criteria.
    Args:
        filename_keyword (str): Keyword to search in filename.
        extensions (List[str]): List of file extensions (e.g., ['.py', '.txt']).
        text_content_keyword (str): Keyword to search in extracted text content.
        min_size (int): Minimum file size in bytes.
        max_size (int): Maximum file size in bytes.
        min_created_time (float): Minimum creation time (Unix timestamp).
        max_created_time (float): Maximum creation time (Unix timestamp).
        min_modified_time (float): Minimum last modified time (Unix timestamp).
        max_modified_time (float): Maximum last modified time (Unix timestamp).
        is_dir (bool): True for directories, False for files, None for both.
        filepath_keyword (str): Keyword to search in the full file path. # Added description
        limit (int): Maximum number of results to return.
    Returns:
        list: A list of dictionaries, each representing a matching file.
    """
    home_dir = os.environ.get("HOME", os.path.expanduser("~"))
    conn = get_db_connection(home_dir)
    cursor = conn.cursor()

    query = "SELECT * FROM files WHERE 1=1"
    params = []

    if filename_keyword:
        query += " AND filename LIKE ?"
        params.append(f"%{filename_keyword}%")
    if extensions:
        placeholders = ','.join('?' * len(extensions))
        query += f" AND extension IN ({placeholders})"
        params.extend([ext.lower() for ext in extensions])
    if text_content_keyword:
        query += " AND text_content LIKE ?"
        params.append(f"%{text_content_keyword}%")
    if min_size is not None:
        query += " AND size >= ?"
        params.append(min_size)
    if max_size is not None:
        query += " AND size <= ?"
        params.append(max_size)
    if min_created_time is not None:
        query += " AND created_time >= ?"
        params.append(min_created_time)
    if max_created_time is not None:
        query += " AND created_time <= ?"
        params.append(max_created_time)
    if min_modified_time is not None:
        query += " AND last_modified >= ?"
        params.append(min_modified_time)
    if max_modified_time is not None:
        query += " AND last_modified <= ?"
        params.append(max_modified_time)
    if is_dir is not None:
        query += " AND is_dir = ?"
        params.append(1 if is_dir else 0)
    if filepath_keyword: # Added condition for filepath search
        query += " AND filepath LIKE ?"
        params.append(f"%{filepath_keyword}%")
    if limit is None:
        limit = 20
    query += f" LIMIT {limit}"
    # 这里打印一下组合好的SQL语句和参数
    logger.info(f"Search file,Executing query: {query} with params: {params}")
    cursor.execute(query, params)
    
    results = cursor.fetchall()
    conn.close()

    return [dict(row) for row in results]

if __name__ == '__main__':
    # Example usage:
    print("Searching for Python files containing 'import':")
    python_files = search_files(extensions=['.py'], text_content_keyword='import', limit=5)
    for f in python_files:
        print(f"  - {f['filepath']} (Size: {f['size']} bytes)")

    print("\nSearching for image files:")
    image_files = search_files(extensions=['.jpg', '.png', '.gif'], limit=5)
    for f in image_files:
        print(f"  - {f['filepath']} (Size: {f['size']} bytes)")

    print("\nSearching for directories named 'app':")
    app_dirs = search_files(filename_keyword='app', is_dir=True, limit=5)
    for d in app_dirs:
        print(f"  - {d['filepath']}")

    print("\nSearching for files larger than 10KB:")
    large_files = search_files(min_size=10240, limit=5)
    for f in large_files:
        print(f"  - {f['filepath']} (Size: {f['size']} bytes)")

    # Example time-based search
    print("\nSearching for files modified in the last 24 hours:")
    one_day_ago = time.time() - (24 * 60 * 60)
    recent_files = search_files(min_modified_time=one_day_ago, limit=5)
    for f in recent_files:
        print(f"  - {f['filepath']} (Modified: {time.ctime(f['last_modified'])})")
