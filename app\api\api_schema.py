
from typing import Any, List, Optional
from pydantic import BaseModel

from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionContentPartParam
)

class ChatAPIInput(BaseModel):
    sn: str = ""   # 必填，设备序列号
    userId: str = ""   # 必填，userId，区分用户
    sessionId: Optional[str] = None   # 非必填，sessionId，区分会话
    content: Optional[List[ChatCompletionContentPartParam]] = []

    class Config:
        arbitrary_types_allowed = True      # 允许任意类型（如 LLM、Memory）。
        extra = "allow"                     # 接受子类额外字段，增强扩展性。

class ChatAPIOutput(BaseModel):
    sn: Optional[str] = None   
    userId: Optional[str] = None   
    sessionId: Optional[str] = None   
    name: Optional[str] = None   
    message: Optional[str] = None 
    code: Optional[int] = None
    media_url: Optional[str] = None  # 可选的媒体URL，用于返回图片或视频等多媒体内容
    event: Optional[str] = None # 新增字段，表示事件类型：message, thought, tool_code, tool_output
    detail: Optional[Any] = None # 新增字段，用于存储不同事件类型的详细信息

class ErrorResponse(BaseModel):
    detail: str | List[Any]     # 可以是字符串或列表（兼容 Pydantic 默认错误格式）
    status_code: int

class ContinueAPIInput(BaseModel):
    userId: str
    sessionId: str
    userInput: str
