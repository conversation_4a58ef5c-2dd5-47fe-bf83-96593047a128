#!/usr/bin/env python3
"""
Search MCP Server
Provides web search capabilities through MCP protocol using stdio
"""

import os
import sys
from typing import Optional
from dotenv import load_dotenv
import fastmcp
import asyncio
from logger import logger
import aiohttp
import time

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService

# Import browser-use framework
from browser_use import Browser, Agent
from browser_use.browser.views import TabInfo
from browser_use.controller.service import Controller
from Screenshot import Screenshot
from browser_use.controller.registry.service import Registry
from browser_use.controller.views import (
    ClickElementAction,
    DoneAction,
    ExtractPageContentAction,
    GoToUrlAction,
    InputTextAction,
    OpenTabAction,
    ScrollDownAction,
    SearchGoogleAction,
    SwitchTabAction,
)
from langchain_openai import ChatOpenAI

# load_dotenv()

# # Configuration
# CUSTOM_CHROME = os.getenv('CUSTOM_CHROME')
# CUSTOM_CHROME_DRIVER = os.getenv('CUSTOM_CHROME_DRIVER')

# # LLM Configuration
# api_key = os.getenv('API_KEY')
# base_url = os.getenv('Base_URL')
# model = os.getenv('Model')


CUSTOM_CHROME="/usr/share/chromium/chrome"
CUSTOM_CHROME_DRIVER="/opt/ruyi-ai-agent/external-mcp-servers/browser_search/chromedriver"
api_key="sk-4f370b3cfc7c45fa9a80e96d0e54e2fa"
base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
model="qwen-plus"



# Initialize LLM
llm = ChatOpenAI(model=model, api_key=api_key, base_url=base_url)

class RVBrowser(Browser):
    def __init__(self, headless: bool = False, keep_open: bool = False):
        self.headless = headless
        self.keep_open = keep_open
        self.MINIMUM_WAIT_TIME = 0.5
        self.MAXIMUM_WAIT_TIME = 5
        self._tab_cache: dict[str, TabInfo] = {}
        self._current_handle = None
        self._ob = Screenshot.Screenshot()
        # Initialize driver during construction
        self.driver: webdriver.Chrome | None = self._setup_webdriver()
        self._cached_state = Browser._update_state(self)

    def _setup_webdriver(self) -> webdriver.Chrome:
        """Sets up and returns a Selenium WebDriver instance with anti-detection measures."""
        try:

            chrome_options = Options()

            # Use headless mode based on initialization
            if self.headless:
                chrome_options.add_argument('--headless=new')
                logger.info(f"🔧 Using headless mode")
            else:
                logger.info(f"🔧 Using non-headless mode (browser window will be visible)")

            # Essential automation and performance settings
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # CRITICAL: Most aggressive Chrome options for MCP environment
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-gpu-sandbox')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')

            # Additional critical options for server environments
            chrome_options.add_argument('--single-process')  # 单进程模式
            chrome_options.add_argument('--no-zygote')  # 禁用zygote进程
            chrome_options.add_argument('--disable-setuid-sandbox')  # 禁用setuid沙箱
            chrome_options.add_argument('--disable-namespace-sandbox')  # 禁用命名空间沙箱

            # Display and window management
            chrome_options.add_argument('--window-size=1280,1024')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--virtual-time-budget=5000')  # 虚拟时间预算

            # Security and permissions (relaxed for MCP environment)
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')

            # Process and memory management
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-translate')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-sync')
            chrome_options.add_argument('--disable-background-networking')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-field-trial-config')
            chrome_options.add_argument('--disable-back-forward-cache')
            chrome_options.add_argument('--disable-hang-monitor')
            chrome_options.add_argument('--disable-prompt-on-repost')
            chrome_options.add_argument('--disable-ipc-flooding-protection')

            # Crash and error reporting
            chrome_options.add_argument('--disable-crash-reporter')
            chrome_options.add_argument('--disable-oopr-debug-crash-dump')
            chrome_options.add_argument('--no-crash-upload')
            chrome_options.add_argument('--disable-client-side-phishing-detection')
            chrome_options.add_argument('--disable-component-update')
            chrome_options.add_argument('--disable-domain-reliability')

            # Performance optimizations
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-features=BlinkGenPropertyTrees')
            chrome_options.add_argument('--disable-low-res-tiling')
            chrome_options.add_argument('--max_old_space_size=4096')
            chrome_options.add_argument('--memory-pressure-off')

            # Logging and debugging
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--disable-infobars')

            # Create isolated user data directory
            # user_data_dir = tempfile.mkdtemp(prefix='chrome_mcp_')
            # chrome_options.add_argument(f'--user-data-dir={user_data_dir}')

            # logger.info(f"🗂️ Using temporary user data dir: {user_data_dir}")

            # Initialize the Chrome driver with better error handling
            custome_chrome = CUSTOM_CHROME
            custome_chrome_driver = CUSTOM_CHROME_DRIVER

            if custome_chrome:
                chrome_options.binary_location = custome_chrome
            if custome_chrome_driver:
                executable_path = custome_chrome_driver
                service = ChromeService(executable_path=executable_path)
            else:
                service = ChromeService()
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute stealth scripts
            driver.execute_cdp_cmd(
                'Page.addScriptToEvaluateOnNewDocument',
                {
                    'source': """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });

                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });

                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });

                    window.chrome = {
                        runtime: {}
                    };

                    Object.defineProperty(navigator, 'permissions', {
                        get: () => ({
                            query: Promise.resolve.bind(Promise)
                        })
                    });
                """
                },
            )
            return driver

        except Exception as e:
            # Clean up any existing driver
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except Exception:
                    pass
            raise

class RVController(Controller):
    def __init__(self, headless=False, keep_open: bool = False):
        self.browser = RVBrowser(headless=headless, keep_open=keep_open)
        self.registry = Registry()
        self._register_default_actions()
        self._register_bing()

    def _register_bing(self):
        """Register all default browser bing actions"""

        # Basic Navigation Actions
        @self.registry.action(
            'Search Bing', param_model=SearchGoogleAction, requires_browser=True
        )
        def search_bing(params: SearchGoogleAction, browser: Browser):
            driver = browser._get_driver()
            driver.get(f'https://www.bing.com/search?q={params.query}')
            browser.wait_for_page_load()

# Global controller instance for new FastMCP
_global_controller: Optional[RVController] = None

def get_or_create_controller(headless: bool = True) -> RVController:
    """Get or create the global controller instance"""
    global _global_controller
    

    if _global_controller is None:
        logger.info(f"🌐 Initializing RVController on first use...")
        try:
            _global_controller = RVController(headless=headless, keep_open=False)
            logger.info(f"✅ RVController initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize RVController: {e}")
            raise

    return _global_controller

def cleanup_controller():
    """Cleanup the global controller"""
    global _global_controller
    

    if _global_controller is not None:
        try:
            if hasattr(_global_controller, 'browser') and _global_controller.browser.driver:
                _global_controller.browser.driver.quit()
                logger.info(f"✅ Browser cleanup completed")
        except Exception as e:
            logger.error(f"⚠️ Error during browser cleanup: {e}")
        finally:
            _global_controller = None

async def _fallback_search(query: str, engine: str = "bing") -> str:
    """Fallback search using simple HTTP requests when browser fails"""

    
    logger.info(f"🔄 >>>>>>>>>>>>>>>>>>>> Using fallback HTTP search method")

    start_time = time.time()

    try:
        # Construct search URL
        if engine.lower() == "bing":
            search_url = f"https://www.bing.com/search?q={query}"
        elif engine.lower() == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return f"Error: Unsupported search engine: {engine}"

        # Set up headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(search_url, timeout=30) as response:
                if response.status == 200:
                    content = await response.text()
                    elapsed_time = time.time() - start_time

                    # Simple content extraction (basic)
                    title = "Search Results (Fallback Mode)"

                    return f"""Search Results (Fallback Mode):
Title: {title}
Query: {query}
Engine: {engine}
URL: {search_url}
Time: {elapsed_time:.2f}s
Status: HTTP {response.status}

Note: This is a fallback mode due to browser initialization failure.
Content length: {len(content)} characters

Content Preview:
{content[:800]}...
"""
                else:
                    return f"Error: HTTP {response.status} - {response.reason}"

    except Exception as e:
        elapsed_time = time.time() - start_time
        return f"Fallback search failed after {elapsed_time:.2f}s: {str(e)}"

# Create MCP server with new FastMCP
mcp = fastmcp.FastMCP("Browser Search Server")

# @mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    """
    Search the web using the specified search engine with Agent mode.

    Args:
        query: The search query to execute
        engine: The search engine to use (bing or google)

    Returns:
        Search results from Agent execution
    """

    

    if not query:
        return "Error: No query provided"

    logger.info(f"🔍 Starting Agent search: '{query}' on {engine}")
    start_time = time.time()

    try:
        # Get or create controller using global state management
        # Use headless=True for MCP server mode (default)
        try:
            # controller = get_or_create_controller(headless=True)
            controller = get_or_create_controller(headless=False)
        except Exception as e:
            logger.error(f"❌ Failed to initialize RVController: {e}")
            # Fallback to simple HTTP request
            return await _fallback_search(query, engine)

        logger.info(f"📱 Controller obtained, creating Agent...")

        # Create task based on engine
        if engine.lower() == "bing":
            task = f"使用 Bing 搜索：{query}，请提取并整理搜索结果"
        elif engine.lower() == "google":
            task = f"使用 Google 搜索：{query}，请提取并整理搜索结果"
        else:
            return f"Error: Unsupported search engine: {engine}. Use 'bing' or 'google'."

        # Create and run Agent
        agent = Agent(
            task=task,
            llm=llm,
            controller=controller,
            use_vision=False,
        )

        logger.info(f"🤖 Running Agent...")
        result = await agent.run()

        elapsed_time = time.time() - start_time
        logger.info(f"✅ Agent search completed in {elapsed_time:.2f} seconds")

        return f"""Agent Search Results:
Query: {query}
Engine: {engine}
Time: {elapsed_time:.2f}s

Agent Result:
{result}
"""

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return error_msg

@mcp.tool()
async def search_web_direct(query: str) -> str:
    """
    Search the web using direct browser automation without Agent.

    Args:
        query: The search query to execute

    Returns:
        Search results extracted directly from the page
    """

    return search_web_direct_with_engine(query, "bing")

async def search_web_direct_with_engine(query: str, engine: str = "bing") -> str:
    """
    Search the web using direct browser automation without Agent.

    Args:
        query: The search query to execute
        engine: The search engine to use (bing or google)

    Returns:
        Search results extracted directly from the page
    """

    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    

    if not query:
        return "Error: No query provided"

    logger.info(f"🔍 Starting direct search: '{query}' on {engine}")
    start_time = time.time()

    try:
        # Get or create controller using global state management
        try:
            controller = get_or_create_controller(headless=False)
            browser = controller.browser.driver
        except Exception as e:
            logger.error(f"❌ Failed to initialize RVController: {e}")
            # Fallback to simple HTTP request
            return await _fallback_search(query, engine)

        logger.info(f"📱 Browser context obtained")

        # Set page load timeout
        browser.set_page_load_timeout(30)

        # Perform search based on engine
        if engine.lower() == "bing":
            search_url = f"https://www.bing.com/search?q={query}"
        elif engine.lower() == "google":
            search_url = f"https://www.google.com/search?q={query}"
        else:
            return f"Error: Unsupported search engine: {engine}. Use 'bing' or 'google'."

        logger.info(f"🌐 Navigating to: {search_url}")
        browser.get(search_url)

        # Wait for page to load with explicit wait
        wait = WebDriverWait(browser, 15)

        if engine.lower() == "bing":
            # Wait for Bing search results with multiple selectors
            try:
                wait.until(EC.any_of(
                    EC.presence_of_element_located((By.ID, "b_results")),
                    EC.presence_of_element_located((By.CLASS_NAME, "b_algo")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-priority]"))
                ))
                logger.info(f"✅ Bing search results loaded")
            except:
                logger.warning("⚠️ Bing results container not found, continuing...")
        elif engine.lower() == "google":
            # Wait for Google search results
            try:
                wait.until(EC.presence_of_element_located((By.ID, "search")))
                logger.info(f"✅ Google search results loaded")
            except:
                logger.warning("⚠️ Google results container not found, continuing...")

        # Additional wait for JavaScript to load
        time.sleep(2)

        # Extract search results
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        # Extract text content from search results with multiple selectors
        try:
            if engine.lower() == "bing":
                # Try multiple Bing CSS selectors
                selectors = [
                    ".b_algo h2 a",           # Traditional selector
                    ".b_algo h2",             # Just h2
                    ".b_algo .b_title a",     # More specific selector
                    "[data-priority] h2 a",   # New data attribute selector
                    ".b_title a",             # Simplified selector
                    "h2 a[href]"              # Generic h2 link selector
                ]

                result_texts = []
                for selector in selectors:
                    try:
                        results = browser.find_elements(By.CSS_SELECTOR, selector)
                        if results:
                            result_texts = [elem.text.strip() for elem in results[:8] if elem.text.strip()]
                            if result_texts:
                                logger.info(f"✅ Found {len(result_texts)} results using selector: {selector}")
                                break
                    except Exception as e:
                        logger.debug(f"Selector {selector} failed: {e}")
                        continue

            elif engine.lower() == "google":
                # Google selectors
                selectors = ["h3", ".LC20lb", ".DKV0Md"]
                result_texts = []
                for selector in selectors:
                    try:
                        results = browser.find_elements(By.CSS_SELECTOR, selector)
                        if results:
                            result_texts = [elem.text.strip() for elem in results[:8] if elem.text.strip()]
                            if result_texts:
                                break
                    except:
                        continue
            else:
                result_texts = []

            if result_texts:
                results_summary = "\n".join([f"{i+1}. {text}" for i, text in enumerate(result_texts) if text])
                logger.info(f"✅ Successfully extracted {len(result_texts)} search results")
            else:
                results_summary = "No specific results extracted"
                logger.warning("⚠️ No results found with any selector")

        except Exception as e:
            logger.warning(f"⚠️ Could not extract specific results: {e}")
            results_summary = f"Results extraction failed: {str(e)}"

        elapsed_time = time.time() - start_time
        logger.info(f"✅ Direct search completed in {elapsed_time:.2f} seconds")

        return f"""Direct Search Results:
Title: {title}
Query: {query}
Engine: {engine}
URL: {current_url}
Time: {elapsed_time:.2f}s

Top Results:
{results_summary}

Page Content Preview:
{page_source[:500]}...
"""

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return error_msg

# @mcp.tool()
async def browse_url(url: str) -> str:
    """
    Navigate to a specific URL and extract content

    Args:
        url: The URL to navigate to

    Returns:
        Page content or error message
    """

    

    if not url:
        return "Error: No URL provided"

    try:
        # Get or create controller using global state management
        controller = get_or_create_controller(headless=False)
        browser = controller.browser.driver

        # Navigate to URL
        browser.get(url)

        # Wait for page to load
        await asyncio.sleep(2)

        # Extract page content
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        return f"Title: {title}\nURL: {current_url}\nContent: {page_source[:1000]}..."

    except Exception as e:
        return f"Error: {str(e)}"

@mcp.resource("search://results/{query}")
def get_search_results(query: str) -> str:
    """Get cached search results for a query"""
    return f"Search results resource for query: {query}"

@mcp.prompt()
def search_prompt(query: str) -> str:
    """Create a search prompt template"""
    return f"Please search for information about: {query}"

async def test_search_direct():
    """Direct test function without MCP context"""

    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC

    query = "搜索中国最美十大景区，从南到北输出。"
    engine = "bing"

    logger.info(f"🧪 Starting direct test...")
    logger.info(f"🔍 Testing search: '{query}' on {engine}")
    start_time = time.time()

    try:
        # Create controller directly for testing
        controller = RVController(keep_open=False)
        browser = controller.browser.driver

        logger.info(f"📱 Browser initialized for testing")

        # Set page load timeout
        browser.set_page_load_timeout(30)

        # Perform search
        search_url = f"https://www.bing.com/search?q={query}"
        logger.info(f"🌐 Navigating to: {search_url}")
        browser.get(search_url)

        # Wait for page to load
        wait = WebDriverWait(browser, 10)
        try:
            wait.until(EC.presence_of_element_located((By.ID, "b_results")))
            logger.info(f"✅ Bing search results loaded")
        except:
            logger.warning("⚠️ Bing results container not found, continuing...")

        # Extract search results
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        # Extract text content from search results
        try:
            results = browser.find_elements(By.CSS_SELECTOR, ".b_algo h2 a")
            result_texts = [elem.text for elem in results[:5]]

            if result_texts:
                results_summary = "\n".join([f"- {text}" for text in result_texts if text.strip()])
            else:
                results_summary = "No specific results extracted"

        except Exception as e:
            logger.warning(f"⚠️ Could not extract specific results: {e}")
            results_summary = "Results extraction failed"

        elapsed_time = time.time() - start_time
        logger.info(f"✅ Search completed in {elapsed_time:.2f} seconds")

        result = f"""Search Results:
Title: {title}
Query: {query}
Engine: {engine}
URL: {current_url}
Time: {elapsed_time:.2f}s

Top Results:
{results_summary}

Page Content Preview:
{page_source[:800]}...
"""

        print("=" * 50)
        print("SEARCH RESULTS:")
        print("=" * 50)
        print(result)

        return result

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Error after {elapsed_time:.2f}s: {str(e)}"
        logger.error(f"❌ {error_msg}")
        print(f"Test failed: {error_msg}")
        return error_msg
    finally:
        # Cleanup
        try:
            if 'controller' in locals() and controller.browser.driver:
                controller.browser.driver.quit()
                logger.info(f"✅ Browser cleanup completed")
        except Exception as e:
            logger.error(f"⚠️ Error during cleanup: {e}")

async def test_tool_functionality():
    """Test the search_web tool directly with visible browser"""
    
    result = await search_web("搜索中国最美十大景区，从南到北输出。", "bing")
    logger.error(f"###### test_tool_functionality output: " + result)

async def test_direct_search():
    """Test the search_web_direct tool with visible browser"""
    

    try:
        # For testing, we want to see the browser, so create controller with headless=False
        global _global_controller
        _global_controller = None  # Reset global controller

        logger.info(f"🧪 Creating controller with visible browser for direct search testing...")
        controller = get_or_create_controller(headless=False)

        # Now call search_web_direct which will use the existing controller
        result = await search_web_direct("搜索中国最美十大景区，从南到北输出。", "bing")
        print("###### test_direct_search output: " + result)
    except Exception as e:
        print(f"###### test_direct_search output: Error after 0.00s: {str(e)}")
    finally:
        # Cleanup after test
        cleanup_controller()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Test mode using direct browser
        print("Starting direct browser test mode...")
        asyncio.run(test_search_direct())
    elif len(sys.argv) > 1 and sys.argv[1] == "tool":
        # Test Agent mode
        print("Starting Agent tool test mode...")
        asyncio.run(test_tool_functionality())
    elif len(sys.argv) > 1 and sys.argv[1] == "direct":
        # Test direct search mode
        print("Starting direct search test mode...")
        asyncio.run(test_direct_search())
    else:
        # Normal MCP server mode
        print("Starting MCP Search Server with stdio protocol...")
        asyncio.run(mcp.run())
