
from typing import Any, Optional
from pydantic import BaseModel, Field

class ToolResult(BaseModel):
    """代表工具执行结果"""
    output: Any = Field(default=None)                   # 工具输出，任意类型
    error: Optional[str] = Field(default=None)          # 错误消息，可选字符串
    base64_image: Optional[str] = Field(default=None)   # Base64 编码图像，可选字符串
    system: Optional[str] = Field(default=None)         # 系统消息，可选字符串
    status: Optional[str] = Field(default=None)         # 状态信号，可选字符串



class ToolFailureResult(ToolResult):
    """代表工具失败结果"""
