#!/bin/bash

CURRENT_BRIGHTNESS=$(cat /sys/class/backlight/pwm-backlight@0/brightness)
PARAM=$1
PERCENT=$2
CONFIG_FILE="/sys/class/backlight/pwm-backlight@0/brightness"

if [ "$PARAM" == "up" ];then
		if [ $CURRENT_BRIGHTNESS -eq 7 ];then
				echo "brightness is highest"
				exit 0
		fi
		NEW_BRIGHTNESS=$(expr $CURRENT_BRIGHTNESS + 1)
		echo $NEW_BRIGHTNESS > $CONFIG_FILE
elif [ "$PARAM" == "down" ];then
		if [ $CURRENT_BRIGHTNESS -eq 1 ];then
				echo "brightness is lowest"
				exit 0
		fi
		NEW_BRIGHTNESS=$(expr $CURRENT_BRIGHTNESS - 1)
		echo $NEW_BRIGHTNESS > $CONFIG_FILE
elif [ "$PARAM" == "set" ];then
		result=$(echo "scale=2; ($PERCENT / 100) * 7" | bc)
		NEW_BRIGHTNESS=$(echo "$result" | awk '{printf("%d\n", $1 + 0.5)}')
		echo $NEW_BRIGHTNESS > $CONFIG_FILE
fi
